.sceneDataContainer {
  height: 100%;
  overflow: hidden;

  .ant-table-wrapper {
    height: 100%;
  }

  .ant-table {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
      white-space: nowrap;
    }

    .ant-table-tbody > tr > td {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 200px;
    }
  }
}

// 分割线行样式
.dividerRow {
  background-color: #f0f8ff !important;

  td {
    background-color: #f0f8ff !important;
    border-color: #91d5ff !important;
  }
}

// 已销场行样式
.verifiedRow {
  background-color: #f6ffed !important;
  opacity: 0.8;

  td {
    background-color: #f6ffed !important;
    border-color: #b7eb8f !important;
  }
}
