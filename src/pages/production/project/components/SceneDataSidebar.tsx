import {
  Badge,
  Drawer,
  Table,
  Tag,
  Typography,
} from 'antd'
import type { ColumnsType } from 'antd/es/table'
import React, { useEffect, useState } from 'react'
import { ATMOSPHERE_TYPE_CONFIG, LOCATION_TYPE_CONFIG, AtmosphereType, LocationType, PlanType } from '@/consts'
import useProjectStore, { type IPrScenePlan } from '../store'
import styles from './SceneDataSidebar.scss'

const { Text } = Typography

interface ISceneDataSidebarProps {
  productionId: number
  visible: boolean
  onClose: () => void
}

const SceneDataSidebar: React.FC<ISceneDataSidebarProps> = ({
  productionId,
  visible,
  onClose,
}) => {
  const { getSceneData } = useProjectStore()

  // 状态管理
  const [loading, setLoading] = useState(false)
  const [sceneData, setSceneData] = useState<IPrScenePlan[]>([])

  // 获取顺场表数据
  const fetchSceneData = async () => {
    if (!productionId) {
      return
    }

    setLoading(true)
    try {
      const data = await getSceneData(productionId)
      if (data) {
        setSceneData(data)
      }
    } catch (error) {
      console.error('获取顺场表数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 定义表格列
  const columns: ColumnsType<IPrScenePlan> = [
    {
      title: '场次编号',
      dataIndex: 'sceneNumber',
      key: 'sceneNumber',
      width: 80,
      fixed: 'left',
      align: 'center',
      render: (text: string, record: IPrScenePlan) => (
        <div>
          <Text strong>{text}</Text>
          {record.verification && <Badge status="success" text="已销场" style={{ marginLeft: 8 }} />}
        </div>
      ),
    },
    {
      title: '气氛',
      dataIndex: 'atmosphere',
      key: 'atmosphere',
      width: 60,
      align: 'center',
      render: (value: AtmosphereType) =>
        value ? ATMOSPHERE_TYPE_CONFIG?.[value]?.label : '-',
    },
    {
      title: '内/外景',
      dataIndex: 'locationType',
      key: 'locationType',
      width: 60,
      align: 'center',
      render: (value: LocationType) =>
        value ? LOCATION_TYPE_CONFIG?.[value]?.label : '-',
    },
    {
      title: '页数',
      dataIndex: 'pageNumber',
      key: 'pageNumber',
      width: 60,
      align: 'center',
      render: (value: number) => value || '-',
    },
    {
      title: '拍摄场景',
      dataIndex: 'shootingLocation',
      key: 'shootingLocation',
      width: 100,
      align: 'center',
      render: (text: string) => text || '-',
    },
    {
      title: '剧本场景',
      dataIndex: 'scriptLocation',
      key: 'scriptLocation',
      width: 120,
      align: 'center',
      render: (text: string) => text || '-',
    },
    {
      title: '场景',
      dataIndex: 'scene',
      key: 'scene',
      width: 100,
      align: 'center',
      render: (text: string) => text || '-',
    },
    {
      title: '主要内容',
      dataIndex: 'mainContent',
      key: 'mainContent',
      width: 240,
      align: 'center',
      render: (text: string) => text || '-',
    },
    {
      title: '主演',
      dataIndex: 'mainActors',
      key: 'mainActors',
      width: 150,
      align: 'center',
      render: (text: string) => text || '-',
    },
    {
      title: '特约/群特',
      dataIndex: 'specialActors',
      key: 'specialActors',
      width: 120,
      align: 'center',
      render: (text: string) => text || '-',
    },
    {
      title: '群演',
      dataIndex: 'groupExtraActors',
      key: 'groupExtraActors',
      width: 100,
      align: 'center',
      render: (text: string) => text || '-',
    },
    {
      title: '服化道提示',
      dataIndex: 'costumeMakeupTip',
      key: 'costumeMakeupTip',
      width: 150,
      align: 'center',
      render: (text: string) => text || '-',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 50,
      align: 'center',
      render: (text: string, record: IPrScenePlan) => {
        // 如果是分割线类型，显示特殊样式
        if (record.planType === PlanType.SCENE_DAY_DIVISION || record.planType === PlanType.SCENE_TRANSITION_DIVISION) {
          return <Tag color="blue">{text || '分割线'}</Tag>
        }
        return text || '-'
      },
    },
  ]

  // 初始化数据
  useEffect(() => {
    if (visible && productionId) {
      fetchSceneData()
    }
  }, [visible, productionId])

  return (
    <Drawer
      title="查看顺场表"
      placement="right"
      width={'80vw'}
      open={visible}
      onClose={onClose}
      loading={loading}
    >
      <div className={styles.sceneDataContainer}>
        <Table
          columns={columns}
          dataSource={sceneData}
          rowKey={(record) => record.id || record.sort || Math.random()}
          pagination={false}
          scroll={{ x: 1500, y: 'calc(100vh - 200px)' }}
          size="small"
          rowClassName={(record) => {
            if (record.planType === PlanType.SCENE_DAY_DIVISION || record.planType === PlanType.SCENE_TRANSITION_DIVISION) {
              return styles.dividerRow
            }
            if (record.verification) {
              return styles.verifiedRow
            }
            return ''
          }}
        />
      </div>
    </Drawer>
  )
}

export default SceneDataSidebar
