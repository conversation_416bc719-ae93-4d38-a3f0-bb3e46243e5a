import type { ActorRoleType, PriceCurrency, RoleType } from '@/consts'
import { create } from 'zustand'
import { envUrl, post, get as requestGet } from '../../../../utils/request'
import dayjs from 'dayjs'
import { ProjectStatus } from '@/consts/project'

// 项目媒体上传API
export const prProductionMediaUploadAPI = '/PrActors/UploadFile'

// 项目分页查询参数（对应API的PagePrProductionsDto）
export interface IProductionListSearchParams {
  pageIndex: number // 页码
  pageSize: number // 每页数量
  productionName?: string // 项目名称
  actorIds?: number[] // 演员ID数组
  status?: number[] // 状态数组
  productionType?: number // 项目类型数组
}

// 合同信息（对应API的PrCommContract）
export interface ICommContract {
  id: number
  contractNumb?: string // 合同编号
  contractName?: string // 合同名称
  partyB?: string // 乙方
  partyBContacts?: string // 乙方联系人
  representative?: string // 乙方授权代表
  partyADate?: string // 签署日期 终止：（甲方）单个时间都用这个
  partyBDate?: string // 终止：终止日期 收款协议变更：乙方（签署时间）
  authorId?: number // 人员id
  verify?: number // 合同类型名称 101保密协议
  editName?: string // 编辑者
  platForm?: number // 101制片平台
  tempFile?: string // 临时文件
  formalFile?: string // 正式文件
  formalFileView?: string // 正式文件查看地址
  penNames?: string // 人员名称
  greement?: string // 协议
  greement2?: string // 协议2
  greementDate?: string // 终止：乙方签署日期
  greementDate2?: string // 协议日期2
  novelName?: string // 项目名称
  bookId?: string // 项目id
  status?: number // 合同状态 0待责编审核 1等待作者签章 2作者签署完成 3责编审核拒绝 4OA审批中 5OA审批通过 6OA审批拒绝 11公司正在签署 12签章完成 100合同归档 101作废
  ext?: string // 签约合同地址
  ext1?: string
  ext2?: string
  ext3?: string
  ext4?: string
  ext5?: string // oa审批单号
  ext6?: string // 责编id
  ext7?: string // 作者id
  ext8?: string // oa审批实例号
  ext9?: string // 签约地址
  reNovelName?: string
  channel?: number
  optionTime?: string // 操作时间
  sendTime?: string // 合同发送时间
  operator?: string // 操作人
  promiseStatus?: number // 承诺函状态 0待归档 1已归档
  promiseFile?: string // 承诺函地址
  signTime?: string // 签订完毕时间
  greementYear?: number
  greementYear2?: number
  greementMon?: number
  greementMon2?: number
  greementDay?: number
  greementDay2?: number
  canEdit?: boolean
  canReSign?: boolean
  isFile?: boolean
  admSign?: boolean
  canView?: boolean
  canLoad?: boolean
  cpMode?: number // 合作模式 0保底 1分成
  scoreType?: number // D= 0, S = 1, A = 2, B = 3, C = 4
  storyType?: number // 类型0长篇小说 1短篇小说
  isExecuted?: number // 是否执行过，新掌中使用
}

// API响应的项目信息（对应API的PrProductions）
export interface IProductionListItem {
  id: number // 项目ID
  productionName?: string // 短剧名称（必填）
  productionCode?: string // 代号
  secondProductionCode?: string // 项目代号
  scriptBookId?: number // 剧本ID
  startDate?: any // 开机日期
  endDate?: any // 结束日期
  description?: string // 项目描述（可选）
  isDelete?: boolean // 是否删除
  creator?: string
  lastModifier?: string
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  feNoEdit?: boolean
  priceCurrency?: PriceCurrency // 1人民币，2美元，3日元
  currencySymbol?: string // 货币符号（衍生字段）：¥（人民币/日元）或 $（美元）
  venueBudget?: number // 场地预算
  hasBudget?: boolean // 是否有预算
  status: ProjectStatus // 状态
  totalEpisodes?: number // 总集数
  totalDuration?: number // 总时长（分钟）
  cityName?: string // 城市
  productionType?: number // 项目类型 0自制，1承制
  companyId?: number // 关联公司
  personBasicInfo?: IProductionPersonBasicItem[]
}

// 项目选角信息（对应API的PrProductionActor）
export interface IProductionActorItem {
  id?: number // 人员池ID
  productionId: number // 关联项目ID
  actorId: number // 关联人员ID
  roleType: ActorRoleType // 角色类型
  playRole?: string // 饰演角色
  cooperationEvaluation?: string // 合作度评价
  quotedPrice?: number // 合作价格/报价
  priceCurrency?: number // 价格货币类型（0人民币，1美元）
  sort: number // 排序
  description?: string // 备注
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  personName: string // 姓名（必填）
  stageName: string // 艺名（必填）
  headUrl?: string // 头像图片地址
  isInternal?: number // 是否内部 (0否，1是)
  fddCustomerVerifyUrl?: string
  fddVerifyStatus?: number //	实名认证状态个人 0：未激活； 1：未认证； 2：审核通过； 3：已提交待审核； 4：审核不通过
  personId?: any
  commContractInfo?: ICommContract[] // 合同信息
  personCount?: number // 人数
  hasInvoice?: boolean // 是否正规发票（1=是，0=否）
  dayCount?: number // 天数
  totalPrice?: number // 总价
  personEvaluation?: IPrPersonEvaluation // 评价
}

// 保存项目演员参数（对应API的SavePrProductionActorDto）
export interface ISaveProductionActorParams {
  productionId: number // 项目ID
  actors?: IProductionActorItem[] // 演员列表
}

// 项目媒体信息（对应API的PrProductionMedia）
export interface IProductionMediaItem {
  id?: number // 项目媒体ID
  productionId: number // 关联项目ID
  mediaType: number // 媒体类型 (1剧照,2定妆照,3片花,4Word,5Excel,6PDF,7合照)
  mediaUrl?: string // 媒体文件URL或路径（注意：接口文档中字段名为 mediaUrL）
  description?: string // 媒体描述（可选）
  sort: number // 排序
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 保存项目媒体参数（对应API的SavePrProductionMediaDto）
export interface ISaveProductionMediaParams {
  productionId: number // 项目ID
  medias?: IProductionMediaItem[] // 媒体列表
}

// 场地媒体信息（对应API的PrVenueMedia）
export interface IPrVenueMedia {
  creator?: string // 创建人
  lastModifier?: string // 最后修改人
  id?: number // 主键
  venueId: number // 场地ID
  mediaType: number // 媒体类型 (1照片,2视频)
  mediaUrl?: string // 媒体文件URL或路径
  subclass?: string // 子分类：默认、客厅、卧室、走廊
  sort?: number // 排序
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  isDelete: boolean // 是否删除
  mediaList?: string[] // 媒体列表（序列化处理）
}

// 场地场景关联信息（对应API的PrProductionVenueInfoDto）
export interface IPrProductionVenueInfo {
  id?: number // ID
  productionId: number // 关联项目ID
  venueId: number // 关联场地ID
  selectionType: number // 场地选择 0备选，1主选
  subVenueName?: string // 子场地
  subSceneName?: string // 子场景
  mainSceneName?: string // 主场景
  venueMedias?: IPrVenueMedia[] // 子场地媒介
  venueName?: string // 场地名称
  updateTime?: string // 更新时间
}

// 场景场地信息（对应API的SceneVenueInfoDto）
export interface ISceneVenueInfo {
  mainVenue?: string // 主场景
  mainVenueByPage?: string // 主场景加分页
  venue?: string // 分场景
  pageCount?: number // 页数
  count?: number // 场次
  sort?: number // 序号
  venueInfos?: IPrProductionVenueInfo[] // 关联场地
}

// 项目场地信息（对应API的PrProductionVenue）
export interface IProductionVenueItem {
  id?: number // 场地记录ID
  productionId: number // 关联项目ID
  venueId: number // 关联演员ID
  quotedPrice?: number // 合作价格/报价
  totalPrice?: number // 总报价
  dayCount?: number // 拍摄天数
  description?: string // 备注
  hasInvoice?: boolean // 是否正规发票（1=是，0=否）
  sort?: number // 排序
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  venueName?: string // 场地名称
  address?: string // 详细位置
  suitableType?: string // 适合拍摄类型(古装,现代, 武侠, 科幻, 民国, 乡村)多个逗号分割
  cost?: number // 费用
  isInternal?: number // 是否内部 0否，1是
  photos?: string // 照片（多个逗号分割）
  videos?: string // 视频（多个逗号分割）
  suitableTypeList?: string[] // 适合拍摄类型列表（序列化处理）
  photoList?: string[] // 照片列表（序列化处理）
  videoList?: string[] // 视频列表（序列化处理）
  venueInfo?: IPrProductionVenueInfo[] // 场地场景表
}

// 保存项目场地参数（对应API的SavePrProductionVenueDto）
export interface ISaveProductionVenueParams {
  productionId: number // 项目Id
  venues?: IProductionVenueItem[] // 媒介
}

// 项目人员基础信息（对应API的PrProductionPersonBasic）
export interface IProductionPersonBasicItem {
  id?: number // 关联ID
  productionId: number // 关联项目ID
  personId: number // 关联人员ID
  sort?: number // 排序
  personName?: string // 人员姓名
  isInternal?: number // 是否内部 (0否，1是)
  roleType: RoleType // 人员类型枚举 (1-9)
  quotedPrice?: number // 报价
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  company?: string
  fddCustomerVerifyUrl?: string
  fddVerifyStatus?: number //	实名认证状态个人 0：未激活； 1：未认证； 2：审核通过； 3：已提交待审核； 4：审核不通过
  commContractInfo?: ICommContract[] // 合同信息
  personCount?: number // 人数
  hasInvoice?: boolean // 是否正规发票（1=是，0=否）
  description?: string // 备注说明
  dayCount?: number // 天数
  totalPrice?: number // 总价
  personEvaluation?: IPrPersonEvaluation // 评价
}

// 保存项目人员基础信息参数（对应API的SavePrProductionPersonBasicDto）
export interface ISaveProductionPersonBasicParams {
  productionId: number // 项目ID
  personBasic?: IProductionPersonBasicItem[] // 人员列表
}

// 项目名称选项（对应API的OptionDto）
export interface IProductionOptionItem {
  val?: string // 键（项目ID）
  label?: string // 值（项目名称）
  labelStr?: string // 格式化后的显示文本（项目名称 + 代号）
}
export interface IProductionBook {
  id: number // 剧本ID
  bookName: string // 剧本名称
  bookCode: string // 剧本代号
}

// API响应类型
export interface IProductionListResponse {
  list: IProductionListItem[]
  total: number
  pageIndex: number
  pageSize: number
}

// 项目额外费用信息（对应API的PrProductionExtraExpenses）
export interface IPrExtraExpensesInfo {
  id?: any
  expenseId?: any // 关联项目ID
  parentType?: number // 一级类型 1人员，2演员，3额外费用
  roleType?: number // 角色类型
  personCount?: number // 人数
  sort?: number
  createTime?: any
  UpdateTime?: any
}
export interface IProductionExtraExpensesItem {
  id?: number // ID
  productionId: number // 关联项目ID
  expenseType: number // 费用类型：1海报物料，2筹备期费用，3车辆租赁，4燃油费，5戏用车辆，6开机，7场务消耗，8差旅费，9剧杂费，10保险费，11餐费，12片场风险控制预算，15住宿费
  quotedPrice?: number // 单价
  personCount?: number // 人数
  hasInvoice?: boolean // 是否正规发票（1=是，0=否）
  description?: string // 备注说明
  sort?: number // 排序
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  dayCount?: number // 天数
  totalPrice?: number // 总价
  expensesInfo?: Array<IPrExtraExpensesInfo>
  expenseName?: string // 额外费用名称（打包价格才有）
}

// 保存项目额外费用参数（对应API的SavePrProductionExtraExpensesDto）
export interface ISaveProductionExtraExpensesParams {
  productionId: number // 项目Id
  extraExpenses?: IProductionExtraExpensesItem[] // 额外费用
}

// 项目招募人员信息（对应API的PrPersonRecruitment）
export interface IProductionPersonRecruitmentItem {
  id?: number // ID
  productionId: number // 关联项目ID
  parentType: number // 一级类型 （1人员，2演员）
  roleType: number // 角色类型
  personCount?: number // 人数
  gender?: number // 性别 (1男，2女，3不限)
  description?: string // 角色介绍
  salaryMin?: number // 薪资下限
  salaryMax?: number // 薪资上限
  salaryType?: number // 薪资类型（1-单价 2-总价）
  currency?: number // 货币类型（1人民币,2美元,3日元）
  isAccommodation?: boolean // 是否提供住宿（0-否 1-是）
  isMeal?: boolean // 是否提供餐食（0-否 1-是）
  specialRequirements?: string // 特殊要求
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 保存项目招募人员参数（对应API的SavePrPersonRecruitmentDto）
export interface ISaveProductionPersonRecruitmentParams {
  productionId: number // 项目Id
  personRecruitment?: IProductionPersonRecruitmentItem[] // 招募人员列表
}

// 人员申请信息（对应API的PrPersonApplication）
export interface IPersonApplicationItem {
  id?: number // 报名主键ID
  actorId?: any
  productionId: number // 关联项目ID
  parentType: number // 一级类型（1人员，2演员）
  roleType: number // 角色类型
  personId: number // 关联人员ID
  introduction?: string // 介绍
  introductionUrls?: string // 介绍文件链接（多文件逗号分割）
  expectedSalary?: number // 期望薪资
  status: number // 报名状态（1-待审核 2-已通过 3-已拒绝 4-已取消）
  reviewComment?: string // 审核意见
  reviewerAccount?: string // 审核人账号
  reviewerNickName?: string // 审核人昵称
  reviewTime?: string // 审核时间
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  // 扩展字段：人员信息（通过关联查询获得）
  personName?: string // 人员姓名
  stageName?: string // 艺名
  headUrl?: string // 头像
  phone?: string // 联系电话
  gender?: number // 性别
  isInternal?: number // 是否内部
}

// 人员申请分页查询参数（对应API的PagePersonApplicationDto）
export interface IPersonApplicationSearchParams {
  pageIndex: number // 页码
  pageSize: number // 每页数量
  productionId: number // 关联项目ID
  status?: number[] // 项目状态数组（可选）
}

// 人员申请分页响应
export interface IPersonApplicationListResponse {
  list: IPersonApplicationItem[]
  total: number
  pageIndex: number
  pageSize: number
}

// 更新人员申请状态参数（对应API的AuditPersonApplicationDto）
export interface IUpdatePersonApplicationStatusParams {
  id: number // 申请ID
  status: number // 报名状态（1-待审核 2-已通过 3-已拒绝 4-已取消）
  reviewComment?: string // 审核意见
}

// 人员评价信息（对应API的PrPersonEvaluation）
export interface IPrPersonEvaluation {
  creator?: string // 创建人
  lastModifier?: string // 最后修改人
  id?: number // ID
  productionId: number // 关联项目ID
  personId: number // 人员Id
  parentType?: number // 一级类型 （1人员，2演员）
  roleType?: number // 角色类型
  score: number // 评分
  comment?: string // 文字描述（评价内容）
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  productionName?: string // 项目名称（扩展字段，用于显示）
  evaluationType?: number // 评价类型（0工作，1住宿）
}

// 项目人员住宿信息（对应API的ProductionPersonRoomDto）
export interface IProductionPersonRoomDto {
  productionId?: number // 关联项目ID
  personId?: number // 人员Id
  roomId?: number // 住宿Id
  checkinDate?: string // 入住时间
  communityName?: string // 小区
  communityArea?: string // 小区地址
  mapCoordinate?: string // 地图坐标（纬度,经度）
  buildingNumber?: number // 楼栋号
  roomNumber?: number // 房间号
  roomPosition?: string // 门号方位（上/下/中）
  roomType?: number // 房型（1单床房、2双床房、3三床房）
  roomLevel?: number // 等级（1优、2良、3一般）
  mainDoorPassword?: string // 大门密码
  innerDoorPassword?: string // 内门密码
  broadbandAccount?: string // 宽带账号
  broadbandPassword?: string // 宽带密码
  equipments?: string // 配备（电视机、洗衣机等，用逗号分隔）
  remark?: string // 备注
  fullRoomNumber?: string // 完整房间号（小区+楼栋号+房间号）
  photos?: string // 小区图片(逗号分割)
  photoList?: string[] // 照片列表（序列化处理）
}

// 项目所有人员信息（对应API的ProductionAllPersonDto）
export interface IPrProductionAllPerson {
  productionId?: number // 关联项目ID
  personId?: number // 人员Id
  parentType?: number // 一级类型 （1人员，2演员）
  roleType?: number // 角色类型
  personName?: string // 人员姓名
  personRoom?: IProductionPersonRoomDto // 住宿信息
  personRoomDates?: string[] // 人员宿舍信息时间
  isInternal: number
}

// 保存项目所有人员参数（对应API的SavePrProductionAllPersonDto）
export interface ISaveProductionAllPersonParams {
  productionId: number // 项目Id
  allPerson?: IPrProductionAllPerson[] // 所有人员列表
}

// 结算数据相关类型定义
export interface IBudgetProduction {
  area: string
  productionName: string
  productionCode: string
  secondProductionCode?: string
  startDate: string
  endDate: string
  priceCurrency: number
}

export interface IBudgetItem {
  maxRoleType: string
  minRoleType: string
  personCount: number
  personCountStr: string
  price: number | null
  priceStr: string
  dayCount: number
  dayCountStr: string
  hasInvoice: boolean | null
  hasInvoiceStr: string
  description: string | null
  roleType: number
  isInternal: number
  cityName: string | null
  personName: string | null
  allPrice: number | null
  allMaxPrice: number
  allPriceStr: string
  minRoleTypeStr: string
}

export interface IProductionBudgetData {
  productions: IBudgetProduction[]
  venueTotal: string
  venueDayCount: string
  venue: IBudgetItem[]
  person: IBudgetItem[]
  personTotal: string
  expense: IBudgetItem[]
  allExpensesPersonTotal: string
  allTotalStr: string
  allTotal: string
  secondAllTotal: string
  secondAllTotalStr: string
}

export interface IProductionListStore {
  allProductionOptions: IProductionOptionItem[] // 所有项目选项列表
  /* API调用方法 */
  fetchProductionList: (params: IProductionListSearchParams) => Promise<IProductionListResponse | null>
  getProductionById: (id: number) => Promise<IProductionListItem | null>
  getProductionActorList: (productionId: number) => Promise<IProductionActorItem[] | null>
  getProductionMediaList: (productionId: number) => Promise<IProductionMediaItem[] | null>
  getProductionVenueList: (productionId: number) => Promise<IProductionVenueItem[] | null>
  getProductionPersonBasicList: (productionId: number) => Promise<IProductionPersonBasicItem[] | null>
  getProductionExtraExpensesList: (productionId: number) => Promise<IProductionExtraExpensesItem[] | null>
  getProductionPersonRecruitmentList: (productionId: number) => Promise<IProductionPersonRecruitmentItem[] | null>
  getPersonApplicationList: (params: IPersonApplicationSearchParams) => Promise<IPersonApplicationListResponse | null>
  updatePersonApplicationStatus: (params: IUpdatePersonApplicationStatusParams) => Promise<boolean>
  saveProduction: (productionData: IProductionListItem) => Promise<boolean>
  saveProductionActor: (params: ISaveProductionActorParams) => Promise<boolean>
  saveProductionMedia: (params: ISaveProductionMediaParams) => Promise<boolean>
  saveProductionVenue: (params: ISaveProductionVenueParams) => Promise<boolean>
  saveProductionPersonBasic: (params: ISaveProductionPersonBasicParams) => Promise<boolean>
  saveProductionExtraExpenses: (params: ISaveProductionExtraExpensesParams) => Promise<boolean>
  saveProductionPersonRecruitment: (params: ISaveProductionPersonRecruitmentParams) => Promise<boolean>
  savePersonEvaluation: (params: IPrPersonEvaluation) => Promise<boolean>
  getAllProductionNames: () => Promise<IProductionOptionItem[] | null>
  getAllScriptBookNames: () => Promise<IProductionBook[] | null>
  deleteProduction: (id: number) => Promise<boolean>
  deleteProductionActor: (id: number) => Promise<boolean>
  deleteProductionMedia: (id: number) => Promise<boolean>
  deleteProductionVenue: (id: number) => Promise<boolean>
  deleteProductionPersonBasic: (id: number) => Promise<boolean>
  deleteProductionExtraExpenses: (id: number) => Promise<boolean>
  deleteProductionPersonRecruitment: (id: number) => Promise<boolean>
  getProductionBudgetData: (productionId: number) => Promise<IProductionBudgetData | null>
  exportProductionBudget: (id: number) => string
  buildProductionBudget: (id: number) => Promise<boolean>
  getBudgetByProductionId: (id: number) => Promise<IProductionBudgetData | null>
  exportBudgetById: (id: number) => string
  // 项目所有人员相关API
  getAllPersonList: (productionId: number) => Promise<IPrProductionAllPerson[] | null>
  getAllPersonRoomList: (productionId: number) => Promise<IPrProductionAllPerson[] | null>
  saveProductionAllPerson: (params: ISaveProductionAllPersonParams) => Promise<boolean>
  deleteProductionAllPerson: (id: number) => Promise<boolean>
  // 获取顺产表场地与场景
  getScenePlanVenueAndInfo: (productionId: number) => Promise<ISceneVenueInfo[]>
  // 变更场地场景关联的选择类型为主选
  updateVenueSelectionType: (id: number) => Promise<boolean>
}

export default create<IProductionListStore>(set => ({
  allProductionOptions: [],

  // 获取项目列表
  fetchProductionList: async (params: IProductionListSearchParams) => {
    try {
      const { data, status } = await post<any, any>('/PrProductions/GetList', params)

      if (status && data) {
        let list = data.dataList || []

        list = list.map((item: IProductionListItem) => {
          item.feNoEdit = item.endDate ? dayjs(item.endDate).add(14, 'day').isBefore(dayjs()) : false
          // item.feNoEdit = false // todo

          // 根据 priceCurrency 设置货币符号
          if (item.priceCurrency === 2) {
            item.currencySymbol = '$' // 美元
          } else if (item.priceCurrency === 3) {
            item.currencySymbol = 'Ұ' // 日元
          } else {
            item.currencySymbol = '¥' // 人民币（默认）
          }

          return item
        })

        return {
          list,
          total: data.totalCount || 0,
          pageIndex: params.pageIndex,
          pageSize: params.pageSize,
        }
      }

      return null
    } catch (error) {
      console.error('获取项目列表失败:', error)

      return null
    }
  },

  // 根据ID获取项目详情
  getProductionById: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetProductionsById?id=${id}`)

      if (status && data?.dataList) {
        const res = data?.dataList || {}

        // 处理货币符号逻辑，保持与列表接口一致
        if (res.priceCurrency === 2) {
          res.currencySymbol = '$' // 美元
        } else if (data.priceCurrency === 3) {
          res.currencySymbol = 'Ұ' // 日元
        } else {
          res.currencySymbol = '¥' // 人民币（默认）
        }

        res.feNoEdit = res.endDate ? dayjs(res.endDate).add(14, 'day').isBefore(dayjs()) : false
        // res.feNoEdit = false // todo

        return res
      }

      return null
    } catch (error) {
      console.error('获取项目详情失败:', error)

      return null
    }
  },

  // 根据项目ID获取演员列表
  getProductionActorList: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetProductionActorList?id=${productionId}`)

      if (status && data) {
        return Array.isArray(data?.dataList) ? data?.dataList : []
      }

      return []
    } catch (error) {
      console.error('获取项目演员列表失败:', error)

      return null
    }
  },

  // 根据项目ID获取媒体列表
  getProductionMediaList: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetProductionMediaList?id=${productionId}`)

      if (status && data) {
        return Array.isArray(data?.dataList) ? data?.dataList : []
      }

      return []
    } catch (error) {
      console.error('获取项目媒体列表失败:', error)

      return null
    }
  },

  // 根据项目ID获取场地列表
  getProductionVenueList: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetProductionVenueList?id=${productionId}`)

      if (status && data) {
        return Array.isArray(data?.dataList) ? data?.dataList : []
      }

      return []
    } catch (error) {
      console.error('获取项目场地列表失败:', error)

      return null
    }
  },

  // 根据项目ID获取人员基础信息列表
  getProductionPersonBasicList: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(
        `/PrProductions/GetProductionPersonBasicList?id=${productionId}`
      )

      if (status && data) {
        return Array.isArray(data?.dataList) ? data?.dataList : []
      }

      return []
    } catch (error) {
      console.error('获取项目人员基础信息列表失败:', error)

      return null
    }
  },

  // 保存项目信息
  saveProduction: async (productionData: IProductionListItem) => {
    try {
      const { data, status } = await post<any, any>('/PrProductions/SaveProduction', productionData)

      if (status) {
        return data || true
      }

      return false
    } catch (error) {
      console.error('保存项目信息失败:', error)

      return false
    }
  },

  // 保存项目演员（批量）
  saveProductionActor: async (params: ISaveProductionActorParams) => {
    try {
      const { data, status } = await post<any, any>('/PrProductions/SaveProductionActor', params)

      if (status) {
        return data || true
      }

      return false
    } catch (error) {
      console.error('保存项目演员失败:', error)

      return false
    }
  },

  // 保存项目媒体（批量）
  saveProductionMedia: async (params: ISaveProductionMediaParams) => {
    try {
      const { data, status } = await post<any, any>('/PrProductions/SaveProductionMedia', params)

      if (status) {
        return data || true
      }

      return false
    } catch (error) {
      console.error('保存项目媒体失败:', error)

      return false
    }
  },

  // 保存项目场地（批量）
  saveProductionVenue: async (params: ISaveProductionVenueParams) => {
    try {
      const { data, status } = await post<any, any>('/PrProductions/SaveProductionVenue', params)

      if (status) {
        return data || true
      }

      return false
    } catch (error) {
      console.error('保存项目场地失败:', error)

      return false
    }
  },

  // 保存项目人员基础信息
  saveProductionPersonBasic: async (params: ISaveProductionPersonBasicParams) => {
    try {
      const { data, status } = await post<any, any>('/PrProductions/SaveProductionPersonBasic', params)

      if (status) {
        return data || true
      }

      return false
    } catch (error) {
      console.error('保存项目人员基础信息失败:', error)

      return false
    }
  },

  // 获取所有项目名称(不分页)
  getAllProductionNames: async () => {
    try {
      const { data, status } = await requestGet<any, any>('/PrProductions/AllName')

      if (status && data) {
        let opts = Array.isArray(data?.dataList) ? data?.dataList : []

        opts = opts.map((item: any) => ({
          labelStr: `${item.label}${item.productionCode ? `(${item.productionCode})` : ''}`,
          ...item,
        }))
        set({ allProductionOptions: opts })

        return opts
      }

      return []
    } catch (error) {
      console.error('获取项目名称失败:', error)

      return []
    }
  },

  // 获取所有已定稿跟上架短剧名称(不分页)
  getAllScriptBookNames: async () => {
    try {
      const { data, status } = await requestGet<any, any>('/PrProductions/AllScriptBookName')

      if (status && data) {
        return Array.isArray(data?.dataList) ? data?.dataList : []
      }

      return []
    } catch (error) {
      console.error('获取已定稿跟上架短剧名称失败:', error)

      return []
    }
  },

  // 删除项目（软删除）
  deleteProduction: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/DeleteProduction?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除项目失败:', error)

      return false
    }
  },

  // 删除项目演员
  deleteProductionActor: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/DeleteProductionActor?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除项目演员失败:', error)

      return false
    }
  },

  // 删除项目媒体
  deleteProductionMedia: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/DeleteProductionMedia?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除项目媒体失败:', error)

      return false
    }
  },

  // 删除项目场地
  deleteProductionVenue: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/DeleteProductionVenue?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除项目场地失败:', error)

      return false
    }
  },

  // 删除项目人员基础信息
  deleteProductionPersonBasic: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/DeleteProductionPersonBasic?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除项目人员基础信息失败:', error)

      return false
    }
  },

  // 根据项目ID获取招募人员列表
  getProductionPersonRecruitmentList: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetPersonRecruitmentList?id=${productionId}`)

      if (status && data) {
        return Array.isArray(data?.dataList) ? data?.dataList : []
      }

      return []
    } catch (error) {
      console.error('获取项目招募人员列表失败:', error)

      return null
    }
  },

  // 保存项目招募人员
  saveProductionPersonRecruitment: async (params: ISaveProductionPersonRecruitmentParams) => {
    try {
      const { data, status } = await post('/PrProductions/SavePersonRecruitment', params)

      return !!status
    } catch (error) {
      console.error('保存项目招募人员失败:', error)

      return false
    }
  },

  // 删除项目招募人员
  deleteProductionPersonRecruitment: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/DeletePrPersonRecruitment?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除项目招募人员失败:', error)

      return false
    }
  },

  // 获取人员申请分页列表
  getPersonApplicationList: async (params: IPersonApplicationSearchParams) => {
    try {
      const { data, status } = await post<any, any>('/PrProductions/GetPersonApplicationList', params)

      if (status && data) {
        const list = Array.isArray(data?.dataList) ? data?.dataList : []

        return {
          list,
          total: data?.totalCount || 0,
          pageIndex: params.pageIndex,
          pageSize: params.pageSize,
        }
      }

      return null
    } catch (error) {
      console.error('获取人员申请列表失败:', error)

      return null
    }
  },

  // 更新人员申请状态（审核）
  updatePersonApplicationStatus: async (params: IUpdatePersonApplicationStatusParams) => {
    try {
      const { data, status } = await post('/PrProductions/UpdatePersonApplicationStatus', params)

      return !!status
    } catch (error) {
      console.error('更新人员申请状态失败:', error)

      return false
    }
  },

  // 保存人员评价
  savePersonEvaluation: async (params: IPrPersonEvaluation) => {
    try {
      const { data, status } = await post('/PrProductions/SavePersonEvaluation', params)

      return !!status
    } catch (error) {
      console.error('保存人员评价失败:', error)

      return false
    }
  },

  // 获取项目额外费用列表
  getProductionExtraExpensesList: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(
        `/PrProductions/GetProductionExtraExpensesList?id=${productionId}`
      )

      if (status && data) {
        return Array.isArray(data?.dataList) ? data?.dataList : []
      }

      return []
    } catch (error) {
      console.error('获取项目额外费用列表失败:', error)

      return null
    }
  },

  // 保存项目额外费用
  saveProductionExtraExpenses: async (params: ISaveProductionExtraExpensesParams) => {
    try {
      const { data, status } = await post('/PrProductions/SaveProductionExtraExpenses', params)

      return !!status
    } catch (error) {
      console.error('保存项目额外费用失败:', error)

      return false
    }
  },

  // 删除项目额外费用
  deleteProductionExtraExpenses: async (id: number) => {
    try {
      const { data, status } = await requestGet(`/PrProductions/DeleteProductionExtraExpenses?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除项目额外费用失败:', error)

      return false
    }
  },

  // 获取项目结算数据
  getProductionBudgetData: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetProductionBudget?id=${productionId}`)

      if (status && data) {
        return data?.dataList || null
      }

      return null
    } catch (error) {
      console.error('获取项目结算数据失败:', error)

      return null
    }
  },

  exportProductionBudget: id => `${envUrl}/PrProductions/ExportProductionBudget?id=${id}`,

  // 生成预算表
  buildProductionBudget: async (id: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrProductions/SetProductionBudget?id=${id}`)

      return !!status
    } catch (error) {
      console.error('生成预算表失败:', error)

      return false
    }
  },

  // 获取预算表
  getBudgetByProductionId: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetBudgetByProductionId?id=${id}`)

      if (status && data) {
        return data?.dataList || null
      }

      return null
    } catch (error) {
      console.error('获取预算表失败:', error)

      return null
    }
  },

  // 导出预算表
  exportBudgetById: id => `${envUrl}/PrProductions/ExportBudgetById?id=${id}`,

  // 获取项目所有人员列表
  getAllPersonList: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetAllPersonList?id=${productionId}`)

      if (status && data) {
        return Array.isArray(data?.dataList) ? data?.dataList : []
      }

      return []
    } catch (error) {
      console.error('获取项目所有人员列表失败:', error)

      return null
    }
  },

  // 获取项目所有宿舍人员列表
  getAllPersonRoomList: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetAllPersonRoomList?id=${productionId}`)

      if (status && data) {
        return Array.isArray(data?.dataList) ? data?.dataList : []
      }

      return []
    } catch (error) {
      console.error('获取项目所有宿舍人员列表失败:', error)

      return null
    }
  },

  // 保存项目所有人员信息
  saveProductionAllPerson: async (params: ISaveProductionAllPersonParams) => {
    try {
      const { data, status } = await post<any, any>('/PrProductions/SaveProductionAllPerson', params)

      if (status) {
        return data || true
      }

      return false
    } catch (error) {
      console.error('保存项目所有人员信息失败:', error)

      return false
    }
  },

  // 删除项目所有人员模块
  deleteProductionAllPerson: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/DeleteProductionAllPerson?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除项目所有人员模块失败:', error)

      return false
    }
  },

  // 获取顺产表场地与场景
  getScenePlanVenueAndInfo: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetScenePlanVenueAndInfo?productionId=${productionId}`)

      if (status && data?.dataList) {
        return data?.dataList || []
      }

      return []
    } catch (error) {
      console.error('获取顺产表场地与场景失败:', error)

      return []
    }
  },

  // 变更场地场景关联的选择类型为主选
  updateVenueSelectionType: async (id: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrVenue/UpdateVenueSelectionType?id=${id}`)

      return !!status
    } catch (error) {
      console.error('变更场地场景关联的选择类型为主选失败:', error)

      return false
    }
  },
}))
