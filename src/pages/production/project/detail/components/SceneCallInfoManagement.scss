.draggableItem {
  margin-bottom: 8px;
  transition: all 0.2s ease;
  cursor: grab;

  &.dragging {
    opacity: 0.5;
    cursor: grabbing;
    transform: rotate(2deg);
    transition: none;

    .dragIcon {
      color: #1890ff;
    }
  }

  &.dropTarget {
    .dividerContainer {
      background: #e6f7ff;
      border: 2px solid #1890ff;
    }

    .sceneCard {
      background: #e6f7ff;
      border: 2px solid #1890ff;
    }
  }
}

.dividerContainer {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  // background: #f0f8ff;
  border: 1px dashed #1890ff;
  border-radius: 4px;
  transition: all 0.2s ease;
  min-height: 80px;

  .dragIcon {
    color: #1890ff;
    margin-right: 8px;
    font-size: 16px;
    transition: all 0.2s ease;
    cursor: grab;

    &.dragging {
      cursor: grabbing;
    }
  }

  .dividerContent {
    flex: 1;
    text-align: center;

    .dividerText {
      color: #1890ff;
      font-weight: 500;
    }
  }
}

.sceneCard {
  transition: all 0.2s ease;

  &.verified {
    border: 1px solid #52c41a;
    background-color: #f6ffed;
  }

  &.dragging {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &.dropTarget {
    border: 2px solid #1890ff;
    background: #e6f7ff;
  }

  .sceneHeader {
    width: 100%;
    justify-content: space-between;

    .sceneInfo {
      .dragIcon {
        color: #999;
        font-size: 16px;
        transition: all 0.2s ease;
        cursor: grab;

        &.dragging {
          color: #1890ff;
          cursor: grabbing;
        }
      }

      .sceneNumber {
        color: #262626;
        font-weight: 600;
      }

      .planType {
        font-weight: 500;
      }

      .sortNumber {
        color: #8c8c8c;
      }
    }

    .verificationStatus {
      color: #52c41a;
      font-weight: 500;

      .closeIcon {
        color: #ff4d4f;
        cursor: pointer;
        font-size: 12px;
        margin-left: 8px;

        &:hover {
          color: #d9363e;
        }
      }
    }
  }

  .sceneDetails {
    width: 100%;

    .detailRow {
      width: 100%;
      justify-content: space-between;

      .detailItem {
        .detailLabel {
          color: #8c8c8c;
          margin-right: 4px;
        }

        .detailValue {
          color: #262626;
        }
      }
    }
  }
}

.sceneListContainer {
  height: calc(100vh - 270px);
  overflow-y: auto;
  padding-right: 8px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.actionButtons {
  margin-bottom: 16px;

  .addButton {
    margin-right: 8px;
  }

  .selectButton {
    margin-right: 8px;
  }
}

.emptyState {
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;

  .emptyIcon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }

  .emptyText {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .emptyDescription {
    font-size: 14px;
    color: #bfbfbf;
  }
}