import { ATMOSPHERE_TYPE_CONFIG, AtmosphereType, LOCATION_TYPE_CONFIG, LocationType, PlanType } from '@/consts'
import { uuid } from '@/utils'
import { exportPlanTemplate } from '@/utils/export'
import {
  CaretRightOutlined,
  CheckOutlined,
  DeleteOutlined,
  DownloadOutlined,
  DragOutlined,
  EditOutlined,
  PlusOutlined,
  ReloadOutlined,
  SaveOutlined,
} from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { Button, Card, Collapse, Divider, Empty, message, Popconfirm, Space, Tooltip, Typography } from 'antd'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useDrag, useDrop } from 'react-dnd'
import { ScenePlanEditModal } from '../../components/ScenePlanEditModal'
import useProjectStore, { type IPrScenePlan } from '../../store'
import styles from './ScenePlanTab.scss'

const { Text } = Typography

// 格式化后的数据结构
interface IFormattedScenePlan {
  scriptLocation: string
  planType: PlanType
  list: IPrScenePlan[]
  // 其他字段从第一个item中获取
  sort?: number
  atmosphere?: number
  locationType?: number
  pageNumber?: number
  shootingLocation?: string
  scene?: string
  mainContent?: string
  costumeMakeupTip?: string
  groupExtraActors?: string
  specialActors?: string
  mainActors?: string
  remark?: string
}

interface IScenePlanTabProps {
  productionId: number
}

// 拖拽项目类型
const DRAG_TYPES = {
  SCENE_PLAN: 'scenePlan',
  SCENE_GROUP: 'sceneGroup',
}

// 计算分割线统计信息的函数
const calculateDividerStats = (
  formattedData: IFormattedScenePlan[],
  currentGroupIndex: number,
  currentItemIndex: number
) => {
  let sceneCount = 0
  let totalPages = 0

  // 从当前位置向前查找，直到找到上一个分割线或到达开头
  for (let groupIndex = currentGroupIndex; groupIndex >= 0; groupIndex--) {
    const group = formattedData[groupIndex]

    // 确定在当前组中的起始位置
    const startItemIndex = groupIndex === currentGroupIndex ? currentItemIndex - 1 : group.list.length - 1

    for (let itemIndex = startItemIndex; itemIndex >= 0; itemIndex--) {
      const item = group.list[itemIndex]

      // 如果遇到分割线，停止统计
      if (item.planType === PlanType.SCENE_DAY_DIVISION) {
        return { sceneCount, totalPages }
      }

      // 如果是场次明细，计入统计
      if (item.planType === PlanType.SCENE_DETAIL) {
        sceneCount++
        totalPages += item.pageNumber || 0
      }
    }
  }

  return { sceneCount, totalPages }
}

// 单个场次计划项组件
const ScenePlanItem: React.FC<{
  item: IPrScenePlan
  index: number
  onEdit: (item: IPrScenePlan) => void
  onDelete: (item: IPrScenePlan) => void
  onMove: (dragIndex: number, hoverIndex: number) => void
  onAddDivider?: (planType: PlanType, scriptLocation?: string, sort?: number) => void
  onDeleteVerification?: (item: IPrScenePlan) => void
  formattedData?: IFormattedScenePlan[]
  groupIndex?: number
}> = ({ item, index, onEdit, onDelete, onMove, onAddDivider, onDeleteVerification, formattedData, groupIndex }) => {
  const [{ isDragging }, drag] = useDrag({
    type: DRAG_TYPES.SCENE_PLAN,
    item: { index },
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
  })

  const [, drop] = useDrop({
    accept: DRAG_TYPES.SCENE_PLAN,
    hover: (draggedItem: { index: number }) => {
      if (draggedItem.index !== index) {
        onMove(draggedItem.index, index)
        draggedItem.index = index
      }
    },
  })

  // 如果是分割线类型，渲染分割线样式
  if (item.planType === PlanType.SCENE_DAY_DIVISION || item.planType === PlanType.SCENE_TRANSITION_DIVISION) {
    const dividerText = item.planType === PlanType.SCENE_DAY_DIVISION ? '日期分割线' : '转场分割线'

    // 计算统计信息（仅对日期分割线显示统计）
    let statsText = ''
    if (item.planType === PlanType.SCENE_DAY_DIVISION && formattedData && typeof groupIndex === 'number') {
      const stats = calculateDividerStats(formattedData, groupIndex, index)
      if (stats.sceneCount > 0 || stats.totalPages > 0) {
        statsText = ` (${stats.sceneCount}个场次，${stats.totalPages.toFixed(1)}页)`
      }
    }

    return (
      <div ref={node => drag(drop(node))} className={isDragging ? styles.dragItem : ''}>
        <div className={styles.dividerContainer}>
          <Divider className={styles.dividerStyle}>
            <Text className={styles.dividerText}>
              {dividerText}
              {statsText}
            </Text>
          </Divider>
          <Space className={styles.actionButtons}>
            <DeleteOutlined className={styles.deleteButton} onClick={() => onDelete(item)} title="删除" />
            <DragOutlined className={styles.dragHandle} />
          </Space>
        </div>
      </div>
    )
  }

  // 检查是否已销场
  const isVerified = !!item.verification

  return (
    <div ref={node => drag(drop(node))} className={isDragging ? styles.dragItem : ''}>
      <Card
        size="small"
        className={styles.scenePlanItem}
        style={{
          border: isVerified ? '1px solid #52c41a' : undefined,
          backgroundColor: isVerified ? '#f6ffed' : undefined,
        }}>
        <Space direction="vertical" size={6} className={styles.fullWidth}>
          {item.planType === PlanType.SCENE_DETAIL ? (
            <Space className={styles.spaceBetween}>
              <Space>
                <Text strong>{item.sceneNumber}</Text>
                {isVerified && (
                  <Space>
                    <Tooltip title={item.verification?.remark || '无备注'}>
                      <Text style={{ color: '#52c41a', fontWeight: 500 }}>
                        <CheckOutlined /> 已销场
                      </Text>
                    </Tooltip>
                    {onDeleteVerification && (
                      <Popconfirm
                        title="确认撤消销场"
                        description="将撤消该场次的销场状态，确定要撤消吗？"
                        onConfirm={() => onDeleteVerification(item)}
                        okText="确定"
                        cancelText="取消">
                        <Button type="text" size="small" danger title="撤消销场" style={{ fontSize: '12px' }}>
                          (撤消销场)
                        </Button>
                      </Popconfirm>
                    )}
                  </Space>
                )}
              </Space>
              <Space size={4}>
                {onAddDivider && !isVerified && (
                  <>
                    <Button
                      type="text"
                      size="small"
                      icon={<PlusOutlined />}
                      onClick={() => onAddDivider(PlanType.SCENE_DAY_DIVISION, item.scriptLocation, (index || 0) + 1)}
                      title="添加日期分割线">
                      日期线
                    </Button>
                    <Button
                      type="text"
                      size="small"
                      icon={<PlusOutlined />}
                      onClick={() =>
                        onAddDivider(PlanType.SCENE_TRANSITION_DIVISION, item.scriptLocation, (index || 0) + 1)
                      }
                      title="添加转场分割线">
                      转场线
                    </Button>
                  </>
                )}
                <EditOutlined
                  onClick={() => onEdit(item)}
                  className={styles.editButton}
                  style={{
                    color: isVerified ? '#d9d9d9' : undefined,
                    cursor: isVerified ? 'not-allowed' : 'pointer',
                  }}
                  title={isVerified ? '已销场，不可编辑' : '编辑'}
                />
                <DragOutlined className={styles.dragHandle} />
              </Space>
            </Space>
          ) : null}
          {item.planType === PlanType.SCENE_DETAIL && (
            <Space size={0} split={<Divider type="vertical" />} wrap>
              {item.atmosphere ? (
                <Dict
                  title="气氛"
                  value={ATMOSPHERE_TYPE_CONFIG?.[item.atmosphere as AtmosphereType]?.label || item.atmosphere}
                />
              ) : null}
              {item.locationType ? (
                <Dict
                  title="内/外景"
                  value={LOCATION_TYPE_CONFIG?.[item.locationType as LocationType]?.label || item.locationType}
                />
              ) : null}
              {item.pageNumber ? <Dict title="页数" value={item.pageNumber} /> : null}
              {item.shootingLocation ? <Dict title="拍摄场景" value={item.shootingLocation} /> : null}
              {item.scene ? <Dict title="场景" value={item.scene} /> : null}
              {item.mainContent ? <Dict title="主要内容" value={item.mainContent} /> : null}
              {item.costumeMakeupTip ? <Dict title="服化道提示" value={item.costumeMakeupTip} /> : null}
              {item.groupExtraActors ? <Dict title="群演" value={item.groupExtraActors} /> : null}
              {item.specialActors ? <Dict title="特约/群特" value={item.specialActors} /> : null}
              {item.mainActors ? <Dict title="主演" value={item.mainActors} /> : null}
            </Space>
          )}
          {item.remark && <Dict title="备注" value={item.remark} />}
        </Space>
      </Card>
    </div>
  )
}

// 场次组组件
const SceneGroup: React.FC<{
  group: IFormattedScenePlan
  index: number
  onEditItem: (item: IPrScenePlan) => void
  onDeleteItem: (item: IPrScenePlan) => void
  onMoveItem: (groupIndex: number, dragIndex: number, hoverIndex: number) => void
  onMoveGroup: (dragIndex: number, hoverIndex: number) => void
  onAddDivider: (planType: PlanType, scriptLocation?: string, sort?: number) => void
  onDeleteVerification: (item: IPrScenePlan) => void
  expandedKeys: string[]
  onExpandChange: (keys: string[]) => void
  formattedData: IFormattedScenePlan[]
}> = ({
  group,
  index,
  onEditItem,
  onDeleteItem,
  onMoveItem,
  onMoveGroup,
  onAddDivider,
  onDeleteVerification,
  expandedKeys,
  onExpandChange,
  formattedData,
}) => {
  const [{ isDragging }, drag] = useDrag({
    type: DRAG_TYPES.SCENE_GROUP,
    item: { index },
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
  })

  const [, drop] = useDrop({
    accept: DRAG_TYPES.SCENE_GROUP,
    hover: (draggedItem: { index: number }) => {
      if (draggedItem.index !== index) {
        onMoveGroup(draggedItem.index, index)
        draggedItem.index = index
      }
    },
  })

  const handleMoveItem = useCallback(
    (dragIndex: number, hoverIndex: number) => {
      onMoveItem(index, dragIndex, hoverIndex)
    },
    [index, onMoveItem]
  )

  // 如果 planType != 0，不使用 Collapse，直接显示分割线内容
  if (group.planType != PlanType.SCENE_DETAIL) {
    return (
      <div ref={node => drag(drop(node))} className={isDragging ? styles.dragItem : ''}>
        <div className={styles.scenePlanGroupSimple}>
          <div className={styles.scenePlanGroupContent}>
            <Space direction="vertical" size={8} className={styles.fullWidth}>
              {group.list.map((item, itemIndex) => {
                // 渲染分割线样式
                const dividerText = item.planType === PlanType.SCENE_DAY_DIVISION ? '日期分割线' : '转场分割线'

                // 计算统计信息（仅对日期分割线显示统计）
                let statsText = ''
                if (item.planType === PlanType.SCENE_DAY_DIVISION) {
                  const stats = calculateDividerStats(formattedData, index, itemIndex)
                  if (stats.sceneCount > 0 || stats.totalPages > 0) {
                    statsText = ` (${stats.sceneCount}个场次，${stats.totalPages.toFixed(1)}页)`
                  }
                }

                return (
                  <div key={item.id || itemIndex} className={styles.dividerContainer}>
                    <Divider className={styles.dividerStyle}>
                      <Text className={styles.dividerText}>
                        {dividerText}
                        {statsText}
                      </Text>
                    </Divider>
                    <Space className={styles.actionButtons}>
                      <DeleteOutlined className={styles.deleteButton} onClick={() => onDeleteItem(item)} title="删除" />
                      <DragOutlined className={styles.dragHandle} />
                    </Space>
                  </div>
                )
              })}
            </Space>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div ref={node => drag(drop(node))} className={isDragging ? styles.dragItem : ''}>
      <Collapse
        size="small"
        className={styles.scenePlanGroup}
        expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
        activeKey={expandedKeys.includes(group.scriptLocation) ? [group.scriptLocation] : []}
        onChange={keys => {
          const newKeys = Array.isArray(keys) ? keys : [keys].filter(Boolean)
          const isExpanded = newKeys.includes(group.scriptLocation)
          let updatedKeys = [...expandedKeys]

          if (isExpanded && !expandedKeys.includes(group.scriptLocation)) {
            updatedKeys.push(group.scriptLocation)
          } else if (!isExpanded && expandedKeys.includes(group.scriptLocation)) {
            updatedKeys = updatedKeys.filter(key => key !== group.scriptLocation)
          }

          onExpandChange(updatedKeys)
        }}
        items={[
          {
            key: group.scriptLocation,
            label: (
              <Space className={styles.spaceBetween}>
                <Space>
                  <DragOutlined />
                  <Text strong>{group.scriptLocation}</Text>
                  <Text type="secondary">
                    ({group.list.filter(item => item.planType === PlanType.SCENE_DETAIL).length}个场次)
                  </Text>
                </Space>
                <Space size={4}>
                  <Button
                    type="text"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={e => {
                      e.stopPropagation()
                      onAddDivider(PlanType.SCENE_DAY_DIVISION, '', index + 1)
                    }}
                    title="添加日期分割线">
                    日期线
                  </Button>
                  <Button
                    type="text"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={e => {
                      e.stopPropagation()
                      onAddDivider(PlanType.SCENE_TRANSITION_DIVISION, '', index + 1)
                    }}
                    title="添加转场分割线">
                    转场线
                  </Button>
                </Space>
              </Space>
            ),
            children: (
              <Space direction="vertical" size={8} className={styles.fullWidth}>
                {group.list.map((item, itemIndex) => (
                  <ScenePlanItem
                    key={item.id || itemIndex}
                    item={item}
                    index={itemIndex}
                    onEdit={onEditItem}
                    onDelete={onDeleteItem}
                    onMove={handleMoveItem}
                    onAddDivider={onAddDivider}
                    onDeleteVerification={onDeleteVerification}
                    formattedData={formattedData}
                    groupIndex={index}
                  />
                ))}
              </Space>
            ),
          },
        ]}
      />
    </div>
  )
}

export const ScenePlanTab: React.FC<IScenePlanTabProps> = ({ productionId }) => {
  const { getScenePlan, refreshScenePlan, saveScenePlan, deleteScenePlan, deleteSceneVerification } = useProjectStore()

  // 状态管理
  const [formattedData, setFormattedData] = useState<IFormattedScenePlan[]>([])
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const [saving, setSaving] = useState(false)
  const [exporting, setExporting] = useState(false)

  // Collapse展开状态和滚动位置状态
  const [expandedKeys, setExpandedKeys] = useState<string[]>([])
  const scrollPositionRef = useRef(0)
  const containerRef = useRef<HTMLDivElement>(null)

  // 编辑弹窗状态
  const [editModal, setEditModal] = useState<{
    visible: boolean
    item: IPrScenePlan | null
    isAdd?: boolean
  }>({
    visible: false,
    item: null,
    isAdd: false,
  })

  // 格式化数据
  const formatData = (data: IPrScenePlan[]): IFormattedScenePlan[] => {
    const grouped = data.reduce((acc, item) => {
      const key = item.scriptLocation || '未分组'
      if (!acc[key]) {
        acc[key] = []
      }
      acc[key].push(item)
      return acc
    }, {} as Record<string, IPrScenePlan[]>)

    return Object.entries(grouped).map(([scriptLocation, list]) => {
      const firstItem = list[0]
      return {
        scriptLocation,
        planType: (firstItem.planType ?? PlanType.SCENE_DETAIL) as PlanType,
        list: list.sort((a, b) => (a.sort || 0) - (b.sort || 0)),
        sort: firstItem.sort,
        atmosphere: firstItem.atmosphere,
        locationType: firstItem.locationType,
        pageNumber: firstItem.pageNumber,
        shootingLocation: firstItem.shootingLocation,
        scene: firstItem.scene,
        mainContent: firstItem.mainContent,
        costumeMakeupTip: firstItem.costumeMakeupTip,
        groupExtraActors: firstItem.groupExtraActors,
        specialActors: firstItem.specialActors,
        mainActors: firstItem.mainActors,
        remark: firstItem.remark,
      }
    })
  }

  // 保存滚动位置
  const saveScrollPosition = () => {
    if (containerRef.current) {
      scrollPositionRef.current = containerRef.current.scrollTop
    }
  }

  // 恢复滚动位置
  const restoreScrollPosition = () => {
    if (containerRef.current && scrollPositionRef.current > 0) {
      setTimeout(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = scrollPositionRef.current
        }
      }, 100) // 延迟一点确保DOM已更新
    }
  }

  // 获取数据
  const fetchData = useCallback(async () => {
    if (!productionId) return

    // 保存当前滚动位置
    saveScrollPosition()

    setLoading(true)
    try {
      const data = await getScenePlan(productionId)
      if (data) {
        setFormattedData(formatData(data))
        // 数据更新后恢复滚动位置
        setTimeout(() => {
          restoreScrollPosition()
        }, 50)
      }
    } catch (error) {
      message.error('获取大计划数据失败')
    } finally {
      setLoading(false)
    }
  }, [productionId])

  // 保存数据
  const handleSave = useCallback(
    async (newData?: IFormattedScenePlan[]) => {
      if (!productionId) return
      if (saving) {
        message.info('保存中')
        return
      }

      setSaving(true)
      try {
        // 将格式化数据转换回原始数据格式
        const flatData: IPrScenePlan[] = []
        let sortIndex = 0
        let arrGroup = newData || formattedData
        arrGroup.forEach(group => {
          group.list.forEach(item => {
            let cacheItem = { ...item, sort: sortIndex++ }
            delete cacheItem.updateTime
            delete cacheItem.createTime
            flatData.push(cacheItem)
          })
        })
        const success = await saveScenePlan({
          productionId,
          scenePlans: flatData,
        })

        if (success) {
          message.success('保存成功')
          await fetchData()
        } else {
          setSaving(false)
        }
      } catch (error) {
        message.error('保存失败')
      } finally {
        setSaving(false)
      }
    },
    [productionId, formattedData]
  )

  // 移动组内项目
  const handleMoveItem = useCallback((groupIndex: number, dragIndex: number, hoverIndex: number) => {
    setFormattedData(prevData => {
      const newData = [...prevData]
      const group = newData[groupIndex]
      const draggedItem = group.list[dragIndex]

      group.list.splice(dragIndex, 1)
      group.list.splice(hoverIndex, 0, draggedItem)

      return newData
    })
  }, [])

  // 移动组
  const handleMoveGroup = useCallback((dragIndex: number, hoverIndex: number) => {
    setFormattedData(prevData => {
      const newData = [...prevData]
      const draggedGroup = newData[dragIndex]

      newData.splice(dragIndex, 1)
      newData.splice(hoverIndex, 0, draggedGroup)

      return newData
    })
  }, [])

  // 添加分割线项目
  const handleAddDividerItem = (planType: PlanType, scriptLocation?: string, index?: number) => {
    // 创建一个新的分割线项目
    const newItem: IPrScenePlan = {
      productionId,
      planType: planType,
      sceneNumber: ``, // 临时场次编号
      scriptLocation: scriptLocation || uuid(),
      remark: '',
    }
    saveScrollPosition()
    handleSaveEdit(newItem, true, index)
  }

  // 保存编辑
  const handleSaveEdit = async (values: Partial<IPrScenePlan>, isAdd: boolean = false, newIndex = 0) => {
    let updatedData: IFormattedScenePlan[]

    setFormattedData(prevData => {
      const newData = [...prevData]

      if (editModal.isAdd || isAdd) {
        // 新增模式：创建新项目
        const newItem: IPrScenePlan = {
          ...editModal.item!,
          ...values,
          id: undefined, // 新增项目没有ID
        }

        // 查找或创建对应的组
        const scriptLocation = newItem.scriptLocation || '日期'
        let targetGroup = newData.find(group => group.scriptLocation === scriptLocation)
        if (!targetGroup) {
          // 创建新组
          targetGroup = {
            scriptLocation,
            planType: (newItem.planType ?? PlanType.SCENE_DAY_DIVISION) as PlanType,
            list: [],
          }
          newData.splice(newIndex, 0, targetGroup)
        }
        targetGroup.list.splice(newIndex, 0, newItem)
      } else {
        // 编辑模式：更新现有项目
        for (const group of newData) {
          const itemIndex = group.list.findIndex(item => item.id === editModal.item?.id)
          if (itemIndex !== -1) {
            group.list[itemIndex] = { ...group.list[itemIndex], ...values }
            break
          }
        }
      }

      updatedData = newData
      return newData
    })

    setEditModal({ visible: false, item: null, isAdd: false })
    handleSave(updatedData!)
  }

  // 刷新数据
  const handleRefresh = useCallback(async () => {
    if (!productionId) return

    setRefreshing(true)
    try {
      const success = await refreshScenePlan(productionId)
      if (success) {
        message.success('刷新成功')
        await fetchData()
      }
    } catch (error) {
      message.error('刷新失败')
    } finally {
      setRefreshing(false)
    }
  }, [productionId])

  // 导出大计划
  const handleExport = useCallback(async () => {
    if (!productionId) return

    setExporting(true)
    try {
      await exportPlanTemplate(productionId)
    } catch (error) {
      message.error('导出失败')
    } finally {
      setExporting(false)
    }
  }, [productionId])

  // 编辑项目
  const handleEditItem = useCallback((item: IPrScenePlan) => {
    // 检查是否已销场
    if (item.verification) {
      message.warning('已销场的场次不可编辑')
      return
    }

    setEditModal({
      visible: true,
      item,
      isAdd: false,
    })
  }, [])

  // 删除项目
  const handleDeleteItem = async (item: IPrScenePlan) => {
    if (!item.id) {
      message.error('无法删除：ID不存在')
      return
    }
    try {
      const success = await deleteScenePlan(item.id)
      if (success) {
        message.success('删除成功')
        await fetchData()
      }
    } catch (error) {
      message.error('删除失败')
    }
  }

  // 撤销销场
  const handleDeleteVerification = async (item: IPrScenePlan) => {
    if (!item.verification?.id) {
      message.error('无法撤销销场：销场记录ID不存在')
      return
    }

    try {
      const success = await deleteSceneVerification(item.verification.id)
      if (success) {
        message.success('撤销销场成功')
        await fetchData()
      }
    } catch (error) {
      console.error('撤销销场失败:', error)
      message.error('撤销销场失败')
    }
  }

  // 处理展开状态变化
  const handleExpandChange = (keys: string[]) => {
    setExpandedKeys(keys)
  }

  // 初始化数据
  useEffect(() => {
    if (productionId) {
      fetchData()
    } else {
      setExpandedKeys([])
      scrollPositionRef.current = 0
    }
  }, [productionId])

  return (
    <div style={{ height: 'calc(100vh - 200px)' }}>
      {/* 顶部工具栏 */}
      <ListHeader title="拖动主场景排序">
        <Space>
          <Button
            type="link"
            icon={<DownloadOutlined />}
            loading={exporting}
            disabled={loading || saving || refreshing}
            onClick={handleExport}>
            导出大计划
          </Button>
          <Button
            color="primary"
            variant="filled"
            shape="round"
            icon={<ReloadOutlined />}
            loading={refreshing}
            disabled={loading || saving}
            onClick={handleRefresh}>
            生成或刷新
          </Button>
          <Button
            type="primary"
            shape="round"
            icon={<SaveOutlined />}
            loading={saving}
            disabled={loading || refreshing}
            onClick={() => handleSave()}>
            保存
          </Button>
        </Space>
      </ListHeader>

      {/* 主要内容区域 */}
      <div
        size="small"
        ref={containerRef}
        style={{
          height: 'calc(100% - 60px)',
          overflow: 'auto',
        }}>
        {formattedData.length > 0 ? (
          <Space direction="vertical" size={16} style={{ width: '100%' }}>
            {formattedData.map((group, index) => (
              <SceneGroup
                key={group.scriptLocation}
                group={group}
                index={index}
                onEditItem={handleEditItem}
                onDeleteItem={handleDeleteItem}
                onMoveItem={handleMoveItem}
                onMoveGroup={handleMoveGroup}
                onAddDivider={handleAddDividerItem}
                onDeleteVerification={handleDeleteVerification}
                expandedKeys={expandedKeys}
                onExpandChange={handleExpandChange}
                formattedData={formattedData}
              />
            ))}
          </Space>
        ) : (
          <Empty />
        )}
      </div>

      {/* 编辑弹窗 */}
      <ScenePlanEditModal
        visible={editModal.visible}
        item={editModal.item}
        onOk={handleSaveEdit}
        onCancel={() => setEditModal({ visible: false, item: null, isAdd: false })}
        isAdd={editModal.isAdd}
      />
    </div>
  )
}

export default ScenePlanTab
