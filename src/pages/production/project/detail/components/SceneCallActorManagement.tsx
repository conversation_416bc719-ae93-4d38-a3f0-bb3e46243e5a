import { SCENE_CALL_ACTOR_ROLE_TYPE_CONFIG, SceneCallActorRoleType } from '@/consts'
import {
  EditOutlined,
} from '@ant-design/icons'
import {
  Button,
  Form,
  Modal,
  Table,
  TimePicker,
  Typography
} from 'antd'
import dayjs, { type Dayjs } from 'dayjs'
import React, { useState } from 'react'
import { IPrSceneCallActor, ISaveSceneCallActorDto } from '../../store'

const { Text } = Typography

interface ISceneCallActorManagementProps {
  callId: number
  actors: IPrSceneCallActor[]
  onSave: (params: ISaveSceneCallActorDto) => Promise<boolean>
  onRefresh: () => Promise<void>
  disabled?: boolean // 是否禁用操作按钮
}

interface IActorFormData {
  departureTime: any
  roleType: number
  roleName?: string
  actorName?: string
  arrivalTime?: Dayjs
  makeupStartTime?: Dayjs
  makeupEndTime?: Dayjs
}

const SceneCallActorManagement: React.FC<ISceneCallActorManagementProps> = ({ callId, actors, onSave, onRefresh, disabled = false }) => {
  const [form] = Form.useForm()
  const [editingActors, setEditingActors] = useState<IPrSceneCallActor[]>(actors)

  const [editModal, setEditModal] = useState<{
    visible: boolean
    actor: IPrSceneCallActor | null
    isAdd: boolean
  }>({
    visible: false,
    actor: null,
    isAdd: false,
  })

  // 编辑演员时间
  const handleEditTime = (actor: IPrSceneCallActor) => {
    setEditModal({
      visible: true,
      actor,
      isAdd: false,
    })
  }

  // 处理模态框提交 - 只编辑时间信息
  const handleModalOk = async () => {
    try {
      const values: IActorFormData = await form.validateFields()

      // 只更新时间相关字段，格式化为字符串
      const updatedActor: IPrSceneCallActor = {
        ...editModal.actor!,
        departureTime: values.departureTime ? values.departureTime.format('HH:mm') : undefined,
        arrivalTime: values.arrivalTime ? values.arrivalTime.format('HH:mm') : undefined,
        makeupStartTime: values.makeupStartTime ? values.makeupStartTime.format('HH:mm') : undefined,
        makeupEndTime: values.makeupEndTime ? values.makeupEndTime.format('HH:mm') : undefined,
      }

      const index = editingActors.findIndex(a => a.id === editModal.actor?.id)
      if (index !== -1) {
        const newActors = [...editingActors]
        newActors[index] = updatedActor

        // 直接调用API保存
        const success = await onSave({
          callId,
          actors: newActors,
        })

        if (success) {
          await onRefresh()
          setEditModal({ visible: false, actor: null, isAdd: false })
          form.resetFields()
        }
      }
    } catch (error) {
      console.error('保存失败:', error)
    }
  }

  // 处理模态框取消
  const handleModalCancel = () => {
    setEditModal({ visible: false, actor: null, isAdd: false })
    form.resetFields()
  }

  // 设置表单初始值
  React.useEffect(() => {
    if (editModal.visible && editModal.actor) {
      form.setFieldsValue({
        roleType: editModal.actor.roleType,
        roleName: editModal.actor.roleName,
        actorName: editModal.actor.actorName,
        departureTime: editModal.actor.departureTime ? dayjs(editModal.actor.departureTime, 'HH:mm') : undefined,
        arrivalTime: editModal.actor.arrivalTime ? dayjs(editModal.actor.arrivalTime, 'HH:mm') : undefined,
        makeupStartTime: editModal.actor.makeupStartTime ? dayjs(editModal.actor.makeupStartTime, 'HH:mm') : undefined,
        makeupEndTime: editModal.actor.makeupEndTime ? dayjs(editModal.actor.makeupEndTime, 'HH:mm') : undefined,
      })
    } else if (editModal.visible && editModal.isAdd) {
      form.resetFields()
    }
  }, [editModal, form])

  // 同步外部数据
  React.useEffect(() => {
    setEditingActors(actors)
  }, [actors])

  const columns = [
    {
      title: '类型',
      dataIndex: 'roleType',
      key: 'roleType',
      align: 'center',
      render: (roleType: number) => {
        const config = SCENE_CALL_ACTOR_ROLE_TYPE_CONFIG[roleType as SceneCallActorRoleType]
        return config ? <Text>{config.label}</Text> : roleType
      },
    },
    {
      title: '演员',
      dataIndex: 'actorName',
      key: 'actorName',
      align: 'center',
      render: (name: string) => <Typography.Text strong>{name}</Typography.Text>,
    },
    {
      title: '角色',
      dataIndex: 'roleName',
      align: 'center',
      key: 'roleName',
      render: (name: string) => name || '-',
    },
    {
      title: '进妆',
      dataIndex: 'makeupStartTime',
      key: 'makeupStartTime',
      align: 'center',
      render: (time: string) => time || '-',
    },
    {
      title: '出妆',
      dataIndex: 'makeupEndTime',
      key: 'makeupEndTime',
      align: 'center',
      render: (time: string) => time || '-',
    },
    {
      title: '出发',
      dataIndex: 'departureTime',
      key: 'departureTime',
      align: 'center',
      render: (time: string) => time || '-',
    },
    {
      title: '到场',
      dataIndex: 'arrivalTime',
      key: 'arrivalTime',
      align: 'center',
      render: (time: string) => time || '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      align: 'center',
      render: (_: any, record: IPrSceneCallActor) => (
        <Button
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => handleEditTime(record)}
          disabled={disabled}
          title={disabled ? '该通告单对应的拍摄日期已过，无法编辑' : '编辑时间'}
        />
      ),
    },
  ]

  return (
    <>
      <Table
        columns={columns}
        dataSource={editingActors}
        rowKey={(record, index) => record.id || `temp-${index}`}
        pagination={false}
        size="small"
      />
      {/* 编辑演员模态框 */}
      <Modal
        title={editModal.isAdd ? '添加演员' : '编辑演员'}
        open={editModal.visible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
        destroyOnHidden>
        <Form form={form} layout="vertical" preserve={false}>
          {/* 显示演员基本信息（只读） */}
          <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 4 }}>
            <div>
              <strong>角色类型：</strong>
              {SCENE_CALL_ACTOR_ROLE_TYPE_CONFIG[editModal.actor?.roleType as SceneCallActorRoleType]?.label || '-'}
            </div>
            <div>
              <strong>角色名：</strong>
              {editModal.actor?.roleName || '-'}
            </div>
            <div>
              <strong>演员名：</strong>
              {editModal.actor?.actorName || '-'}
            </div>
          </div>

          <Form.Item label="化妆开始时间" name="makeupStartTime">
            <TimePicker placeholder="请选择化妆开始时间" format="HH:mm" style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item label="化妆结束时间" name="makeupEndTime">
            <TimePicker placeholder="请选择化妆结束时间" format="HH:mm" style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            label="出发时间"
            name="departureTime"
          >
            <TimePicker
              placeholder="请选择出发时间"
              format="HH:mm"
              style={{ width: '100%' }}
            />
          </Form.Item>
          <Form.Item
            label="到场时间"
            name="arrivalTime"
          >
            <TimePicker
              placeholder="请选择到场时间"
              format="HH:mm"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form >
      </Modal >
    </>
  )
}

export default SceneCallActorManagement
