import {
  ATMOSPHERE_TYPE_CONFIG,
  AtmosphereType,
  LOCATION_TYPE_CONFIG,
  LocationType,
  PlanType
} from '@/consts'
import { SearchOutlined } from '@ant-design/icons'
import { useDebounceFn } from 'ahooks'
import { Card, Input, Modal, Space, Typography } from 'antd'
import React, { useMemo, useState } from 'react'
import useProjectStore, { IPrScenePlan } from '../../store'

const { Text } = Typography

interface IScenePlanSelectorModalProps {
  visible: boolean
  onCancel: () => void
  onSelect: (scenePlans: IPrScenePlan[]) => void
  productionId: number
}

const ScenePlanSelectorModal: React.FC<IScenePlanSelectorModalProps> = ({
  visible,
  onCancel,
  onSelect,
  productionId
}) => {
  const { getScenePlan } = useProjectStore()
  const [scenePlans, setScenePlans] = useState<IPrScenePlan[]>([])
  const [selectedPlans, setSelectedPlans] = useState<IPrScenePlan[]>([])
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')

  // 获取大计划数据
  React.useEffect(() => {
    if (visible && productionId) {
      fetchScenePlans()
    } else if (!visible) {
      // 关闭弹窗时重置搜索状态
      setSearchText('')
      setSelectedPlans([])
    }
  }, [visible, productionId])

  const fetchScenePlans = async () => {
    setLoading(true)
    try {
      const data = await getScenePlan(productionId)
      // 只显示场次明细类型的场景
      const detailPlans = data?.filter(plan => plan.planType === PlanType.SCENE_DETAIL && !plan.verification) || []
      setScenePlans(detailPlans)
    } catch (error) {
      console.error('获取大计划失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSelect = (plan: IPrScenePlan, checked: boolean) => {
    if (checked) {
      setSelectedPlans([...selectedPlans, plan])
    } else {
      setSelectedPlans(selectedPlans.filter(p => p.id !== plan.id))
    }
  }

  // 搜索防抖
  const { run: onSearchDebounce } = useDebounceFn((value: string) => {
    setSearchText(value)
  }, { wait: 300 })

  // 过滤后的场景数据
  const filteredScenePlans = useMemo(() => {
    if (!searchText.trim()) {
      return scenePlans
    }

    const searchLower = searchText.toLowerCase()
    return scenePlans.filter(plan =>
      plan.sceneNumber?.toLowerCase().includes(searchLower) ||
      plan.scriptLocation?.toLowerCase().includes(searchLower) ||
      plan.scene?.toLowerCase().includes(searchLower) ||
      plan.mainContent?.toLowerCase().includes(searchLower) ||
      plan.remark?.toLowerCase().includes(searchLower)
    )
  }, [scenePlans, searchText])

  const handleConfirm = () => {
    onSelect(selectedPlans)
    setSelectedPlans([])
    onCancel()
  }

  return (
    <Modal
      title="从大计划选择场景"
      open={visible}
      onCancel={onCancel}
      onOk={handleConfirm}
      width={800}
      okText="确认添加"
      cancelText="取消"
      okButtonProps={{ disabled: selectedPlans.length === 0 }}
    >
      {/* 搜索框 */}
      <div style={{ marginBottom: 16 }}>
        <Input
          placeholder="搜索场次编号、剧本场景、场景、主要内容或备注..."
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => onSearchDebounce(e.target.value)}
          style={{ width: '100%' }}
        />
        {!loading && scenePlans.length > 0 && (
          <div style={{ marginTop: 8, color: '#666', fontSize: '12px' }}>
            {searchText.trim()
              ? `找到 ${filteredScenePlans.length} 个匹配的场景（共 ${scenePlans.length} 个）`
              : `共 ${scenePlans.length} 个场景`
            }
          </div>
        )}
      </div>

      <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            加载中...
          </div>
        ) : filteredScenePlans.length > 0 ? (
          <Space direction="vertical" size={8} style={{ width: '100%' }}>
            {filteredScenePlans.map((plan) => (
              <Card
                key={plan.id}
                size="small"
                style={{
                  cursor: 'pointer',
                  border: selectedPlans.find(p => p.id === plan.id) ? '2px solid #1890ff' : '1px solid #d9d9d9'
                }}
                onClick={() => handleSelect(plan, !selectedPlans.find(p => p.id === plan.id))}
              >
                <Space direction="vertical" size={4} style={{ width: '100%' }}>
                  <Space>
                    <Text strong>{plan.sceneNumber}</Text>
                    {plan.atmosphere && (
                      <Text style={{ color: ATMOSPHERE_TYPE_CONFIG[plan.atmosphere as AtmosphereType]?.color }}>
                        {ATMOSPHERE_TYPE_CONFIG[plan.atmosphere as AtmosphereType]?.label}
                      </Text>
                    )}
                    {plan.locationType && (
                      <Text style={{ color: LOCATION_TYPE_CONFIG[plan.locationType as LocationType]?.color }}>
                        {LOCATION_TYPE_CONFIG[plan.locationType as LocationType]?.label}景
                      </Text>
                    )}
                  </Space>
                  <Space wrap>
                    {plan.scriptLocation && (
                      <Text type="secondary">剧本场景: {plan.scriptLocation}</Text>
                    )}
                    {plan.scene && (
                      <Text type="secondary">场景: {plan.scene}</Text>
                    )}
                    {plan.pageNumber && (
                      <Text type="secondary">页数: {plan.pageNumber}</Text>
                    )}
                  </Space>
                  {plan.mainContent && (
                    <Text type="secondary">主要内容: {plan.mainContent}</Text>
                  )}
                </Space>
              </Card>
            ))}
          </Space>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
            {searchText.trim() ? '未找到匹配的场景' : '暂无可选择的场景'}
          </div>
        )}
      </div>
      {selectedPlans.length > 0 && (
        <div style={{ marginTop: 16, padding: '12px', background: '#f0f8ff', borderRadius: '4px' }}>
          <Text>已选择 {selectedPlans.length} 个场景</Text>
        </div>
      )}
    </Modal>
  )
}

export default ScenePlanSelectorModal
