import { BookOutlined, PictureOutlined } from '@ant-design/icons'
import { Tabs, TabsProps } from 'antd'
import React from 'react'
import ProductionMedia from '../../list/components/ProductionMedia'
import { IProductionListItem } from '../../list/store'
import styles from '../index.scss'
import OverviewTab from './OverviewTab'

interface NewOverviewTabProps {
  project?: IProductionListItem
  onEdit?: (project: IProductionListItem) => void
  onDelete?: (project: IProductionListItem) => void
}

const NewOverviewTab: React.FC<NewOverviewTabProps> = ({ project, onEdit, onDelete }) => {
  const tabItems: TabsProps['items'] = [
    {
      key: 'overview',
      label: '基本信息',
      icon: <BookOutlined />,
      children: (
        <div className={styles.scrollC}>
          <OverviewTab project={project} onEdit={onEdit} onDelete={onDelete} />
        </div>
      ),
    },
    {
      key: 'media',
      label: '附件资料',
      icon: <PictureOutlined />,
      children: (
        <div className={styles.scrollC}>
          <ProductionMedia productionId={project.id} project={project} />
        </div>
      ),
    },
  ]

  return <Tabs type="card" tabBarGutter={8} items={tabItems} tabPosition="left" />
}

export default NewOverviewTab
