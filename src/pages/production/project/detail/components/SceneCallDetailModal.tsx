import { EditOutlined } from '@ant-design/icons'
import { Button, Descriptions, Drawer, Flex, Spin, Tabs, Typography } from 'antd'
import React, { useCallback, useEffect, useState } from 'react'
import useProjectStore, { IPrSceneCall } from '../../store'
import SceneCallActorManagement from './SceneCallActorManagement'
import styles from './SceneCallDetailModal.scss'
import SceneCallEditModal from './SceneCallEditModal'
import SceneCallInfoManagement from './SceneCallInfoManagement'
import SceneCallMealManagement from './SceneCallMealManagement'

interface ISceneCallDetailDrawerProps {
  visible: boolean
  sceneCall: IPrSceneCall | null
  onCancel: () => void
  onRefresh: () => Promise<void>
  productionId?: number
  disabled?: boolean // 是否禁用操作按钮
}

const SceneCallDetailModal: React.FC<ISceneCallDetailDrawerProps> = ({
  visible,
  sceneCall,
  onCancel,
  onRefresh,
  productionId,
  disabled = false,
}) => {
  const {
    getSceneCallById,
    saveSceneCall,
    saveSceneCallMeal,
    saveSceneCallActor,
    saveSceneCallInfo,
    deleteSceneCallInfo,
  } = useProjectStore()

  // 本地状态管理
  const [currentSceneCall, setCurrentSceneCall] = useState<IPrSceneCall | null>(sceneCall)
  const [loading, setLoading] = useState(false)

  // 编辑弹窗状态
  const [editModal, setEditModal] = useState<{
    visible: boolean
  }>({ visible: false })

  // 对通告单场景信息进行排序
  const sortSceneCallInfos = (sceneCall: IPrSceneCall): IPrSceneCall => ({
    ...sceneCall,
    sceneInfos: sceneCall.sceneInfos ? [...sceneCall.sceneInfos].sort((a, b) => (a.sort || 0) - (b.sort || 0)) : [],
  })

  // 获取通告单详情
  const fetchSceneCallDetail = useCallback(async () => {
    if (!sceneCall?.id) {
      return
    }

    setLoading(true)
    try {
      const data = await getSceneCallById(sceneCall.id)

      if (data) {
        setCurrentSceneCall(sortSceneCallInfos(data))
      }
    } catch (error) {
      console.error('获取通告单详情失败:', error)
    } finally {
      setLoading(false)
    }
  }, [sceneCall?.id, getSceneCallById])

  // 刷新当前通告单数据
  const refreshCurrentData = useCallback(async () => {
    await fetchSceneCallDetail()
    // 同时刷新列表数据
    await onRefresh()
  }, [fetchSceneCallDetail, onRefresh])

  // 当弹窗打开或sceneCall变化时，获取最新数据
  useEffect(() => {
    if (visible && sceneCall?.id) {
      fetchSceneCallDetail()
    } else if (visible && sceneCall) {
      // 如果没有ID，直接使用传入的sceneCall，并排序场景信息
      setCurrentSceneCall(sortSceneCallInfos(sceneCall))
    }
  }, [visible, sceneCall?.id, fetchSceneCallDetail])

  // 保存通告单基本信息
  const handleSave = async (values: Partial<IPrSceneCall>) => {
    if (!currentSceneCall?.id) {
      return
    }

    try {
      // 先获取最新的通告单数据
      const latestData = await getSceneCallById(currentSceneCall.id)

      if (!latestData) {
        console.error('无法获取最新数据')

        return
      }

      // 只更新基本信息字段，保留其他数据
      const updateData = {
        ...latestData,
        dayNumber: values.dayNumber ?? latestData.dayNumber,
        scheduleRemark: values.scheduleRemark ?? latestData.scheduleRemark,
        remark: values.remark ?? latestData.remark,
        responsibleDept: values.responsibleDept ?? latestData.responsibleDept,
        contact: values.contact ?? latestData.contact,
        meals: values.meals ?? latestData.meals,
      }

      const success = await saveSceneCall(updateData)

      if (success) {
        await refreshCurrentData()
        setEditModal({ visible: false })
      }
    } catch (error) {
      console.error('保存失败:', error)
    }
  }

  // 编辑通告单基本信息
  const handleEdit = () => {
    setEditModal({ visible: true })
  }

  // 处理编辑弹窗提交
  const handleEditSubmit = async (values: Partial<IPrSceneCall>) => {
    // 处理 meals 字段的时间格式化
    const formattedValues = {
      ...values,
      meals:
        values.meals?.map((meal: any) => ({
          ...meal,
          mealTime: meal.mealTime
            ? typeof meal.mealTime === 'string'
              ? meal.mealTime
              : meal.mealTime.format('HH:mm')
            : undefined,
        })) || [],
    }

    await handleSave(formattedValues)
  }

  if (!currentSceneCall) {
    return null
  }

  const tabItems = [
    {
      key: 'basic',
      label: '基本信息',
      children: (
        <Flex vertical gap={12}>
          <Flex justify="flex-end">
            {disabled && (
              <Button type="primary" ghost icon={<EditOutlined />} onClick={handleEdit}>
                编辑
              </Button>
            )}
          </Flex>
          <Descriptions bordered column={1}>
            {currentSceneCall.scheduleRemark && (
              <Descriptions.Item label="行程安排">
                <Typography.Paragraph className="no-margin" style={{ whiteSpace: 'pre-wrap' }}>
                  {currentSceneCall.scheduleRemark}
                </Typography.Paragraph>
              </Descriptions.Item>
            )}
            {currentSceneCall.responsibleDept && (
              <Descriptions.Item label="责任部门">
                <Typography.Paragraph className="no-margin" style={{ whiteSpace: 'pre-wrap' }}>
                  {currentSceneCall.responsibleDept}
                </Typography.Paragraph>
              </Descriptions.Item>
            )}
            {currentSceneCall.contact && (
              <Descriptions.Item label="联系方式">
                <Typography.Paragraph className="no-margin" style={{ whiteSpace: 'pre-wrap' }}>
                  {currentSceneCall.contact}
                </Typography.Paragraph>
              </Descriptions.Item>
            )}
            {currentSceneCall.remark && (
              <Descriptions.Item label="特别注意">
                <Typography.Paragraph className="no-margin" style={{ whiteSpace: 'pre-wrap' }}>
                  {currentSceneCall.remark}
                </Typography.Paragraph>
              </Descriptions.Item>
            )}
          </Descriptions>
        </Flex>
      ),
    },
    {
      key: 'meals',
      label: '用餐安排',
      children: currentSceneCall.id ? (
        <SceneCallMealManagement
          callId={currentSceneCall.id}
          meals={currentSceneCall.meals || []}
          onSave={saveSceneCallMeal}
          onRefresh={refreshCurrentData}
          disabled={disabled}
        />
      ) : null,
    },
    {
      key: 'actors',
      label: '演员时刻',
      children: currentSceneCall.id ? (
        <SceneCallActorManagement
          callId={currentSceneCall.id}
          actors={currentSceneCall.actors || []}
          onSave={saveSceneCallActor}
          onRefresh={refreshCurrentData}
          disabled={disabled}
        />
      ) : null,
    },
    {
      key: 'sceneInfos',
      label: '场次信息',
      children: currentSceneCall.id ? (
        <SceneCallInfoManagement
          callId={currentSceneCall.id}
          sceneInfos={currentSceneCall.sceneInfos || []}
          onSave={saveSceneCallInfo}
          onDelete={deleteSceneCallInfo}
          onRefresh={refreshCurrentData}
          productionId={productionId}
          disabled={disabled}
        />
      ) : null,
    },
  ]

  return (
    <>
      <Drawer
        title={`通告单 - 第 ${currentSceneCall.dayNumber || 1} 天`}
        open={visible}
        onClose={onCancel}
        width="60%"
        placement="right"
        className={styles.drawer}>
        <Spin spinning={loading}>
          <Tabs defaultActiveKey="basic" indicator={{ size: 32 }} items={tabItems} size="small" />
        </Spin>
      </Drawer>

      {/* 编辑基本信息弹窗 */}
      {editModal.visible ? (
        <SceneCallEditModal
          visible={editModal.visible}
          sceneCall={currentSceneCall}
          onOk={handleEditSubmit}
          onCancel={() => setEditModal({ visible: false })}
        />
      ) : null}
    </>
  )
}

export default SceneCallDetailModal
