import {
  ATMOSPHERE_TYPE_CONFIG,
  AtmosphereType,
  LOCATION_TYPE_CONFIG,
  LocationType,
  PLAN_TYPE_CONFIG,
  PlanType
} from '@/consts'
import {
  CheckOutlined,
  DeleteOutlined,
  DragOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
} from '@ant-design/icons'
import {
  Button,
  Card,
  message,
  Popconfirm,
  Space,
  Tooltip,
  Typography,
} from 'antd'
import React, { useState } from 'react'
import { useDrag, useDrop } from 'react-dnd'
import useProjectStore, { IPrSceneCallInfo, IPrScenePlan, IPrSceneVerification, ISaveSceneCallInfoDto } from '../../store'
import FilmShotLogDetailModal from './FilmShotLogDetailModal'
import SceneCallInfoEditModal, { ISceneInfoFormData } from './SceneCallInfoEditModal'
import ScenePlanSelectorModal from './ScenePlanSelectorModal'
import SceneVerificationModal from './SceneVerificationModal'
import styles from './SceneCallInfoManagement.scss'

const { Text } = Typography

// 拖拽项目类型
const DRAG_TYPES = {
  SCENE_CALL_INFO: 'sceneCallInfo',
  SCENE_PLAN: 'scenePlan',
}

// 可拖拽的场次信息项组件
const DraggableSceneInfoItem: React.FC<{
  item: IPrSceneCallInfo
  index: number
  onEdit: (item: IPrSceneCallInfo) => void
  onDelete: (item: IPrSceneCallInfo, index: number) => void
  onMove: (dragIndex: number, hoverIndex: number) => void
  onDrop: (dragIndex: number, hoverIndex: number) => void
  onVerification: (item: IPrSceneCallInfo, remark: string) => Promise<void>
  onDeleteVerification: (item: IPrSceneCallInfo) => Promise<void>
  onViewFilmShotLog: (item: IPrSceneCallInfo) => void
  disabled?: boolean
}> = ({ item, index, onEdit, onDelete, onMove, onDrop, onVerification, onDeleteVerification, onViewFilmShotLog, disabled = false }) => {
  const [verificationModal, setVerificationModal] = useState(false)
  const [{ isDragging }, drag] = useDrag({
    type: DRAG_TYPES.SCENE_CALL_INFO,
    item: { index, id: item.id, originalIndex: index }, // 记录原始位置
    canDrag: !disabled, // 禁用时不允许拖拽
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    end: (dragItem, monitor) => {
      // 拖拽结束时触发保存
      if (monitor.didDrop()) {
        const dropResult = monitor.getDropResult() as { targetIndex?: number } | null
        if (dropResult && dropResult.targetIndex !== undefined) {
          // 使用原始位置和目标位置进行比较和保存
          onDrop(dragItem.originalIndex, dropResult.targetIndex)
        }
      }
    },
  })

  const [{ isOver }, drop] = useDrop({
    accept: DRAG_TYPES.SCENE_CALL_INFO,
    canDrop: () => !disabled, // 禁用时不允许接受拖拽
    drop: () => ({ targetIndex: index }),
    hover: (draggedItem: { index: number, id: string | number, originalIndex: number }) => {
      if (!disabled && draggedItem.index !== index) {
        // 只在 hover 时进行视觉上的移动，不保存数据
        onMove(draggedItem.index, index)
        draggedItem.index = index // 更新当前位置用于视觉移动
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  })

  // 处理销场确认
  const handleVerificationConfirm = async (remark: string) => {
    await onVerification(item, remark)
    setVerificationModal(false)
  }

  // 检查是否已销场
  const isVerified = !!item.verification

  // 如果是分割线类型，显示分割线样式
  if (item.planType === 1) {
    return (
      <div
        ref={(node) => drag(drop(node))}
        className={`${styles.draggableItem} ${isDragging ? styles.dragging : ''} ${isOver ? styles.dropTarget : ''}`}
      >
        <div className={styles.dividerContainer}>
          <DragOutlined
            className={`${styles.dragIcon} ${isDragging ? styles.dragging : ''}`}
          />
          <div className={styles.dividerContent}>
            <Text className={styles.dividerText}>
              ━━━ {item.remark || '分割线'} ━━━
            </Text>
          </div>
          <Space>
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => onEdit(item)}
              disabled={isVerified || disabled}
              title={disabled ? '该通告单对应的拍摄日期已过，无法编辑' : (isVerified ? '已销场，不可编辑' : '编辑')}
            />
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => onDelete(item, index)}
              disabled={isVerified || disabled}
              title={disabled ? '该通告单对应的拍摄日期已过，无法删除' : (isVerified ? '已销场，不可删除' : '删除')}
            />
          </Space>
        </div>
      </div>
    )
  }

  // 普通场次信息显示
  return (
    <>
      <div
        ref={(node) => drag(drop(node))}
        className={`${styles.draggableItem} ${isDragging ? styles.dragging : ''} ${isOver ? styles.dropTarget : ''}`}
      >
        <Card
          size="small"
          className={`${styles.sceneCard} ${isVerified ? styles.verified : ''} ${isDragging ? styles.dragging : ''} ${isOver ? styles.dropTarget : ''}`}
        >
          <Space direction="vertical" size={8} className={styles.sceneDetails}>
            <Space className={styles.sceneHeader}>
              <Space className={styles.sceneInfo}>
                <DragOutlined
                  className={`${styles.dragIcon} ${isDragging ? styles.dragging : ''}`}
                />
                {item.sort && <Text type="secondary" className={styles.sortNumber}>#{item.sort}</Text>}
                {item.planType !== undefined && (
                  <Text className={styles.planType} style={{ color: PLAN_TYPE_CONFIG[item.planType as PlanType]?.color }}>
                    {PLAN_TYPE_CONFIG[item.planType as PlanType]?.label}
                  </Text>
                )}
                {item.sceneNumber && <Text strong className={styles.sceneNumber}>{item.sceneNumber}</Text>}
                {isVerified && (
                  <Space className={styles.verificationStatus}>
                    <Tooltip title={item.verification?.remark || '无备注'}>
                      <Text>
                        <CheckOutlined /> 已销场
                      </Text>
                    </Tooltip>
                    {disabled ? null : <Popconfirm
                      title="确认撤消销场"
                      description="将撤消该场次的销场状态，确定要撤消吗？"
                      onConfirm={() => onDeleteVerification(item)}
                      okText="确定"
                      cancelText="取消"
                      disabled={disabled}
                    >
                      <Button
                        type="text"
                        size="small"
                        danger
                        title="撤消销场"
                        disabled={disabled}
                      >(撤消销场)</Button>
                    </Popconfirm>}
                  </Space>
                )}
              </Space>
              <Space>
                {!isVerified && item.planType === 0 && (
                  <Button
                    type="link"
                    size="small"
                    onClick={() => setVerificationModal(true)}
                    title="销场"
                    disabled={disabled}
                  >
                    销场
                  </Button>
                )}
                {item.planType === 0 && (
                  <Button
                    type="link"
                    size="small"
                    icon={<EyeOutlined />}
                    onClick={() => onViewFilmShotLog(item)}
                    title="查看场记"
                  >
                    查看场记
                  </Button>
                )}
                <Button
                  type="text"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => onEdit(item)}
                  disabled={isVerified || disabled}
                  title={disabled ? '该通告单对应的拍摄日期已过，无法编辑' : (isVerified ? '已销场，不可编辑' : '编辑')}
                />
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => onDelete(item, index)}
                  disabled={isVerified || disabled}
                  title={disabled ? '该通告单对应的拍摄日期已过，无法删除' : (isVerified ? '已销场，不可删除' : '删除')}
                />
              </Space>
            </Space>

            <Space wrap>
              {item.atmosphere && (
                <Text style={{ color: ATMOSPHERE_TYPE_CONFIG[item.atmosphere as AtmosphereType]?.color }}>
                  {ATMOSPHERE_TYPE_CONFIG[item.atmosphere as AtmosphereType]?.label}
                </Text>
              )}
              {item.locationType && (
                <Text style={{ color: LOCATION_TYPE_CONFIG[item.locationType as LocationType]?.color }}>
                  {LOCATION_TYPE_CONFIG[item.locationType as LocationType]?.label}景
                </Text>
              )}
              {item.pageNumber && (
                <Text type="secondary">页数: {item.pageNumber}</Text>
              )}
              {item.scriptLocation && (
                <Text type="secondary">剧本场景: {item.scriptLocation}</Text>
              )}
              {item.scene && (
                <Text type="secondary">场景: {item.scene}</Text>
              )}
            </Space>

            {item.mainContent && (
              <Text type="secondary">主要内容: {item.mainContent}</Text>
            )}

            {item.remark && (
              <Text type="secondary">备注: {item.remark}</Text>
            )}
          </Space>
        </Card>
      </div>

      {/* 销场确认弹窗 */}
      {verificationModal ? <SceneVerificationModal
        visible={verificationModal}
        onCancel={() => setVerificationModal(false)}
        onConfirm={handleVerificationConfirm}
        sceneNumber={item.sceneNumber}
      /> : null}
    </>
  )
}

interface ISceneCallInfoManagementProps {
  callId: number
  sceneInfos: IPrSceneCallInfo[]
  onSave: (params: ISaveSceneCallInfoDto) => Promise<boolean>
  onDelete: (id: number) => Promise<boolean>
  onRefresh: () => Promise<void>
  productionId?: number // 添加productionId用于获取大计划
  disabled?: boolean // 是否禁用操作按钮
}

const SceneCallInfoManagement: React.FC<ISceneCallInfoManagementProps> = ({
  callId,
  sceneInfos,
  onSave,
  onDelete,
  onRefresh,
  productionId,
  disabled = false,
}) => {
  const { saveSceneVerification, deleteSceneVerification } = useProjectStore()
  const [editingInfos, setEditingInfos] = useState<IPrSceneCallInfo[]>(sceneInfos)
  const [dragSaving, setDragSaving] = useState(false)

  const [editModal, setEditModal] = useState<{
    visible: boolean
    info: IPrSceneCallInfo | null
    isAdd: boolean
  }>({
    visible: false,
    info: null,
    isAdd: false,
  })

  // 场景选择弹窗状态
  const [sceneSelectorModal, setSceneSelectorModal] = useState(false)

  // 场记弹窗状态
  const [filmShotLogModal, setFilmShotLogModal] = useState<{
    detailVisible: boolean
    currentSceneInfo: IPrSceneCallInfo | null
  }>({
    detailVisible: false,
    currentSceneInfo: null
  })

  // 添加分割线
  const handleAddDivider = () => {
    setEditModal({
      visible: true,
      info: null,
      isAdd: true,
    })
  }

  // 编辑场次信息
  const handleEdit = (info: IPrSceneCallInfo) => {
    setEditModal({
      visible: true,
      info,
      isAdd: false,
    })
  }

  // 删除场次信息
  const handleDelete = async (info: IPrSceneCallInfo, index: number) => {
    if (info.id) {
      // 如果有ID，调用API删除
      try {
        const success = await onDelete(info.id)
        if (success) {
          message.success('删除成功')
          await onRefresh()
        }
      } catch (error) {
        message.error('删除失败')
      }
    } else {
      // 如果没有ID，直接从本地数组删除
      const newInfos = [...editingInfos]
      newInfos.splice(index, 1)
      setEditingInfos(newInfos)
    }
  }

  // 处理销场
  const handleVerification = async (info: IPrSceneCallInfo, remark: string) => {
    if (!productionId || !info.sceneNumber) {
      message.error('缺少必要参数，无法销场')
      return
    }

    try {
      const verificationData: IPrSceneVerification = {
        productionId,
        sceneNumber: info.sceneNumber,
        callId: info.callId,
        remark: remark || undefined,
      }

      const success = await saveSceneVerification(verificationData)
      if (success) {
        message.success('销场成功')
        await onRefresh()
      }
    } catch (error) {
      console.error('销场失败:', error)
      message.error('销场失败')
    }
  }

  // 撤销销场
  const handleDeleteVerification = async (info: IPrSceneCallInfo) => {
    if (!info.verification?.id) {
      message.error('无法撤销销场：销场记录ID不存在')
      return
    }

    try {
      const success = await deleteSceneVerification(info.verification.id)
      if (success) {
        message.success('撤销销场成功')
        await onRefresh()
      }
    } catch (error) {
      console.error('撤销销场失败:', error)
      message.error('撤销销场失败')
    }
  }

  // 查看场记
  const handleViewFilmShotLog = (item: IPrSceneCallInfo) => {
    setFilmShotLogModal({
      detailVisible: true,
      currentSceneInfo: item
    })
  }

  // 处理模态框提交
  const handleModalOk = async (values: ISceneInfoFormData) => {
    try {
      let infoData: IPrSceneCallInfo

      if (editModal.isAdd) {
        // 添加分割线 - 只需要备注字段
        infoData = {
          callId,
          sort: editingInfos.length + 1,
          planType: 1, // 分割线类型
          remark: values.remark,
        }
      } else {
        // 编辑现有场次信息
        infoData = {
          callId,
          sort: values.sort,
          planType: values.planType,
          sceneNumber: values.sceneNumber,
          scriptLocation: values.scriptLocation,
          atmosphere: values.atmosphere,
          locationType: values.locationType,
          pageNumber: values.pageNumber,
          scene: values.scene,
          mainContent: values.mainContent,
          costumeMakeupTip: values.costumeMakeupTip,
          groupExtraActors: values.groupExtraActors,
          specialActors: values.specialActors,
          remark: values.remark,
          consultation: values.consultation,
        }
      }

      let newInfos: IPrSceneCallInfo[]
      if (editModal.isAdd) {
        // 添加新分割线
        newInfos = [...editingInfos, infoData]
      } else {
        // 编辑现有场次信息
        const index = editingInfos.findIndex(i => i.id === editModal.info?.id)
        if (index !== -1) {
          newInfos = [...editingInfos]
          newInfos[index] = { ...editModal.info!, ...infoData }
        } else {
          newInfos = editingInfos
        }
      }

      // 直接调用API保存
      const success = await onSave({
        callId,
        infos: newInfos,
      })

      if (success) {
        await onRefresh()
        setEditModal({ visible: false, info: null, isAdd: false })
      }
    } catch (error) {
      console.error('保存失败:', error)
    }
  }

  // 处理模态框取消
  const handleModalCancel = () => {
    setEditModal({ visible: false, info: null, isAdd: false })
  }

  // 处理拖拽移动（仅视觉移动，不保存）
  const handleMove = (dragIndex: number, hoverIndex: number) => {
    const newInfos = [...editingInfos]
    const draggedItem = newInfos[dragIndex]

    newInfos.splice(dragIndex, 1)
    newInfos.splice(hoverIndex, 0, draggedItem)

    // 更新排序
    const updatedInfos = newInfos.map((info, index) => ({
      ...info,
      sort: index + 1,
    }))

    setEditingInfos(updatedInfos)
  }

  // 处理拖拽结束（真正保存数据）
  const handleDrop = async (originalIndex: number, targetIndex: number) => {
    if (originalIndex === targetIndex) return // 位置没有变化，不需要保存

    // 由于在拖拽过程中已经通过 handleMove 更新了视觉状态
    // 这里直接保存当前的 editingInfos 状态即可
    await saveDraggedData(editingInfos)
  }

  // 保存拖拽数据
  const saveDraggedData = async (infos: IPrSceneCallInfo[]) => {
    if (dragSaving) return // 防止重复保存

    setDragSaving(true)
    try {
      message.loading({ content: '正在保存排序...', key: 'drag-save' })
      await onSave({
        callId,
        infos,
      })
      await onRefresh()
      message.success({ content: '排序保存成功', key: 'drag-save', duration: 2 })
    } catch (error) {
      console.error('保存拖拽数据失败:', error)
      message.error({ content: '保存失败', key: 'drag-save', duration: 3 })
    } finally {
      setDragSaving(false)
    }
  }

  // 从大计划选择场景
  const handleSelectFromPlan = () => {
    setSceneSelectorModal(true)
  }

  // 处理选择的场景
  const handleScenePlansSelected = async (selectedPlans: IPrScenePlan[]) => {
    const newInfos = selectedPlans.map((plan, index) => ({
      callId,
      sort: editingInfos.length + index + 1,
      planType: 0, // 设置为场景类型
      sceneNumber: plan.sceneNumber,
      scriptLocation: plan.scriptLocation,
      atmosphere: plan.atmosphere,
      locationType: plan.locationType,
      pageNumber: plan.pageNumber,
      lineNumber: plan.lineNumber,
      scene: plan.scene,
      mainContent: plan.mainContent,
      groupExtraActors: plan.groupExtraActors,
      mainActors: plan.mainActors,
      costumeMakeupTip: plan.costumeMakeupTip,
      specialActors: plan.specialActors || '',
      remark: plan.remark,
    }))

    const updatedInfos = [...editingInfos, ...newInfos]

    // 直接调用API保存
    const success = await onSave({
      callId,
      infos: updatedInfos,
    })

    if (success) {
      await onRefresh()
    }
  }

  // 同步外部数据
  React.useEffect(() => {
    setEditingInfos(sceneInfos)
  }, [sceneInfos])



  return (
    <Card size="small">
      <Space direction="vertical" size={16} style={{ width: '100%' }}>
        <Space className={styles.actionButtons}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddDivider}
            disabled={dragSaving || disabled}
            className={styles.addButton}
            title={disabled ? '该通告单对应的拍摄日期已过，无法添加' : '添加分割线'}
          >
            添加分割线
          </Button>
          {productionId && (
            <Button
              type="default"
              onClick={handleSelectFromPlan}
              disabled={dragSaving || disabled}
              className={styles.selectButton}
              title={disabled ? '该通告单对应的拍摄日期已过，无法选择' : '从大计划选择'}
            >
              从大计划选择
            </Button>
          )}
        </Space>

        {editingInfos.length > 0 ? (
          <div className={styles.sceneListContainer}>
            {editingInfos.map((info, index) => (
              <DraggableSceneInfoItem
                key={info.id || `temp-${index}`}
                item={info}
                index={index}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onMove={handleMove}
                onDrop={handleDrop}
                onVerification={handleVerification}
                onDeleteVerification={handleDeleteVerification}
                onViewFilmShotLog={handleViewFilmShotLog}
                disabled={disabled}
              />
            ))}
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
            暂无场次信息
          </div>
        )}
      </Space>

      {/* 编辑场次信息模态框 */}
      {editModal.visible ? <SceneCallInfoEditModal
        visible={editModal.visible}
        onCancel={handleModalCancel}
        onOk={handleModalOk}
        editInfo={editModal.info}
        isAdd={editModal.isAdd}
      /> : null}
      {/* 场景选择弹窗 */}
      {productionId && sceneSelectorModal && (
        <ScenePlanSelectorModal
          visible={sceneSelectorModal}
          onCancel={() => setSceneSelectorModal(false)}
          onSelect={handleScenePlansSelected}
          productionId={productionId}
        />
      )}

      {/* 场记详情弹窗 */}
      {filmShotLogModal.detailVisible && filmShotLogModal.currentSceneInfo && (
        <FilmShotLogDetailModal
          visible={filmShotLogModal.detailVisible}
          onClose={() => setFilmShotLogModal(prev => ({ ...prev, detailVisible: false, currentSceneInfo: null }))}
          sceneNumber={filmShotLogModal.currentSceneInfo.sceneNumber}
          locationTypeStr={filmShotLogModal.currentSceneInfo?.locationTypeStr}
          callId={callId}
          productionId={productionId || 0}
          atmosphere={filmShotLogModal.currentSceneInfo.atmosphereStr}
          disabled={disabled}
        />
      )}


    </Card>
  )
}

export default SceneCallInfoManagement
