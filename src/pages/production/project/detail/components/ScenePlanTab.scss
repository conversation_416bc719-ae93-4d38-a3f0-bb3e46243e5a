.dragItem {
  opacity: 0.5;
}

.dividerContainer {
  position: relative;
  
  .actionButtons {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: white;
    padding: 0 8px;
  }
}

.dividerStyle {
  margin: 8px 0;
  border-color: #1890ff;
}

.dividerText {
  color: #1890ff;
  font-weight: 500;
}

.scenePlanItem {
  .fullWidth {
    width: 100%;
  }
  
  .spaceBetween {
    width: 100%;
    justify-content: space-between;
  }
}

.editButton {
  color: #1890ff;
  cursor: pointer;
  
  &:hover {
    color: #40a9ff;
  }
}

.deleteButton {
  color: #ff4d4f;
  cursor: pointer;
  
  &:hover {
    color: #ff7875;
  }
}

.dragHandle {
  color: #999;
  cursor: move;
  
  &:hover {
    color: #666;
  }
}

.scenePlanGroup {
  .fullWidth {
    width: 100%;
  }
  
  .spaceBetween {
    width: 100%;
    justify-content: space-between;
  }
}

.scenePlanGroupSimple {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
  
  .scenePlanGroupContent {
    .fullWidth {
      width: 100%;
    }
  }
}
