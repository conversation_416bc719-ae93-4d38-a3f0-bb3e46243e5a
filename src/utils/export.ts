import { message } from 'antd'
import request from './request'

/**
 * 通用文件导出函数
 * @param url 导出接口地址
 * @param params 请求参数
 * @param filename 默认文件名
 * @param method 请求方法，默认 GET
 * @param headers 额外的请求头
 * @returns Promise<void>
 */
export const exportFile = async ({
  url,
  params = {},
  filename,
  method = 'GET',
  headers = {},
}: {
  url: string
  params?: Record<string, any>
  filename?: string
  method?: 'GET' | 'POST'
  headers?: Record<string, string>
}): Promise<void> => {
  try {
    // 构建请求配置
    const config: any = {
      url,
      method,
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      ignoreFormat: true,
    }

    // 根据请求方法设置参数
    if (method === 'GET') {
      config.params = params
    } else {
      config.data = params
    }

    const response = await request(config)

    // 检查响应状态
    if (!response.data) {
      throw new Error('导出失败：服务器返回空数据')
    }

    // 获取文件名 - 优先使用服务器返回的文件名
    const contentDisposition = response.headers?.['content-disposition'] || response.headers?.['Content-Disposition']
    let finalFilename = filename || 'export.xlsx'

    if (contentDisposition) {
      // 尝试多种文件名解析方式
      let serverFilename = null

      // 方式1: filename="xxx"
      const filenameMatch1 = contentDisposition.match(/filename="([^"]+)"/)

      if (filenameMatch1) {
        serverFilename = filenameMatch1[1]
      }

      // 方式2: filename=xxx (无引号)
      if (!serverFilename) {
        const filenameMatch2 = contentDisposition.match(/filename=([^;]+)/)

        if (filenameMatch2) {
          serverFilename = filenameMatch2[1]
        }
      }

      // 方式3: filename*=UTF-8''xxx (URL编码)
      if (!serverFilename) {
        const filenameMatch3 = contentDisposition.match(/filename\*=UTF-8''([^;]+)/)

        if (filenameMatch3) {
          try {
            serverFilename = decodeURIComponent(filenameMatch3[1])
          } catch (e) {
            console.warn('文件名URL解码失败:', e)
          }
        }
      }

      // 如果成功解析到服务器文件名，使用服务器文件名
      if (serverFilename) {
        finalFilename = serverFilename
        console.log('使用服务器返回的文件名:', finalFilename)
      } else {
        console.log('未解析到服务器文件名，使用默认文件名:', finalFilename)
      }
    } else {
      console.log('响应头中无content-disposition，使用默认文件名:', finalFilename)
    }

    // 创建blob并下载
    const blob = new Blob([response.data], { type: response.headers?.['content-type'] || 'application/octet-stream' })
    const blobUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')

    link.href = blobUrl
    link.download = finalFilename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(blobUrl)

    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
    throw error
  }
}

/**
 * 导出结算文件
 * @param productionId 项目ID
 * @param productionName 项目名称（可选，用于调试）
 */
export const exportProductionBudget = async (productionId: number, filename?: string): Promise<void> => {
  // console.log(`开始导出结算，项目ID: ${productionId}, 项目名称: ${filename || '未知'}`)

  await exportFile({
    url: '/PrProductions/ExportProductionBudget',
    params: { id: productionId },
    filename,
    // 不设置默认文件名，完全依赖服务器返回的文件名
  })
}

/**
 * 导出预算文件
 * @param productionId 项目ID
 * @param filename 文件名（可选，用于调试）
 */
export const exportProductionBudgetById = async (productionId: number, filename?: string): Promise<void> => {
  await exportFile({
    url: '/PrProductions/ExportBudgetById',
    params: { id: productionId },
    filename,
  })
}

/**
 * 导出保险文件
 * @param productionId 项目ID
 * @param filename 名称（可选，用于调试）
 */
export const exportProductionInsurance = async (productionId: number, filename?: string): Promise<void> => {
  // console.log(`开始导出结算，项目ID: ${productionId}, 项目名称: ${filename || '未知'}`)

  await exportFile({
    url: '/PrProductions/ExportProductionInsurance',
    params: { id: productionId },
    filename,
  })
}

/**
 * 导出场地
 * @param filename 文件名（可选，用于调试）
 */
export const exportVenue = async (filename?: string): Promise<void> => {
  await exportFile({
    url: '/PrVenue/ExportVenue',
    params: {},
    filename,
  })
}


/**
 * 导出导出宿舍人员信息
 * @param productionId 项目ID
 * @param filename 文件名（可选，用于调试）
 */
export const exportProductionRoomPerson = async (id: number, filename?: string): Promise<void> => {
  await exportFile({
    url: '/PrDormRoom/ExportProductionRoomPerson',
    params: { id },
    filename,
  })
}

/**
 * 导出顺场表
 * @param productionId 项目ID
 * @param filename 文件名（可选，用于调试）
 */
export const exportProductionTemplate = async (productionId: number, filename?: string): Promise<void> => {
  await exportFile({
    url: '/PrProductions/ExportTemplate',
    params: { productionId },
    filename,
  })
}

/**
 * 导出大计划
 * @param productionId 项目ID
 * @param filename 文件名（可选，用于调试）
 */
export const exportPlanTemplate = async (productionId: number, filename?: string): Promise<void> => {
  await exportFile({
    url: '/PrProductions/PlanExportTemplate',
    params: { productionId },
    filename,
  })
}

/**
 * 导出顺产表场地统计
 * @param productionId 项目ID
 * @param filename 文件名（可选，用于调试）
 */
export const exportScenePlanVenue = async (productionId: number, filename?: string): Promise<void> => {
  await exportFile({
    url: '/PrProductions/ExportScenePlanVenue',
    params: { productionId },
    filename,
  })
}


/**
 * 导出单个通告单
 * @param callId 通告单ID
 * @param filename 文件名（可选，用于调试）
 */
export const exportSingleCallTemplate = async (callId: number, filename?: string): Promise<void> => {
  await exportFile({
    url: '/PrProductions/CallExportTemplate',
    params: { id: callId },
    filename,
  })
}

/**
 * 导出邀请信
 * @param id 项目ID
 * @param filename 文件名（可选，用于调试）
 */
export const exportRoomWord = async (id: number, filename?: string): Promise<void> => {
  await exportFile({
    url: '/PrDormRoom/WordTemplate',
    params: { id: id },
    filename,
  })
}


/**
 * 导出Excel文件
 * @param url 导出接口地址
 * @param params 请求参数
 * @param filename 文件名
 * @param method 请求方法
 */
export const exportExcel = async ({
  url,
  params = {},
  filename,
  method = 'GET',
}: {
  url: string
  params?: Record<string, any>
  filename: string
  method?: 'GET' | 'POST'
}): Promise<void> => {
  await exportFile({
    url,
    params,
    filename: filename.endsWith('.xlsx') ? filename : `${filename}.xlsx`,
    method,
  })
}

/**
 * 导出PDF文件
 * @param url 导出接口地址
 * @param params 请求参数
 * @param filename 文件名
 * @param method 请求方法
 */
export const exportPDF = async ({
  url,
  params = {},
  filename,
  method = 'GET',
}: {
  url: string
  params?: Record<string, any>
  filename: string
  method?: 'GET' | 'POST'
}): Promise<void> => {
  await exportFile({
    url,
    params,
    filename: filename.endsWith('.pdf') ? filename : `${filename}.pdf`,
    method,
  })
}
